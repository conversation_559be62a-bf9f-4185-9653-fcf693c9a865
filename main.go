package main

import (
	"fmt"
	"log/slog"

	"github.com/damonto/vowifi/driver"
	"github.com/damonto/vowifi/driver/ccid"
	"github.com/damonto/vowifi/usim"
)

func main() {
	slog.SetLogLoggerLevel(slog.LevelDebug)
	d, err := ccid.New()
	if err != nil {
		panic(err)
	}
	d.<PERSON>("Alcor Link AK9563 00 00")
	transmitter, err := driver.NewTransmitter(slog.Default(), d)
	if err != nil {
		panic(err)
	}
	defer transmitter.Close()

	s, err := usim.New(transmitter, slog.Default())
	if err != nil {
		panic(err)
	}
	fmt.Println(s)

	// imei, _ := usim.NewIMEI("3566564213052700")
	// ikev2, err := ikev2.New(s, imei, "", slog.Default())
	// if err != nil {
	// 	panic(err)
	// }
	// fmt.Println(ikev2.Connect())
}
