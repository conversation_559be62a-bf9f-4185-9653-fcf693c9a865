package main

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/damonto/vowifi/driver"
	"github.com/damonto/vowifi/driver/ccid"
	"github.com/damonto/vowifi/swu"
	"github.com/damonto/vowifi/usim"
)

func main() {
	// Set up logging
	slog.SetLogLoggerLevel(slog.LevelDebug)
	logger := slog.Default()

	// Check for demo mode
	if len(os.Args) > 1 && os.Args[1] == "--demo" {
		logger.Info("Running in demo mode (no USIM card required)")
		runDemo(logger)
		return
	}

	// Initialize CCID driver
	d, err := ccid.New()
	if err != nil {
		logger.Error("Failed to create CCID driver", "error", err)
		logger.Info("Tip: Use '--demo' flag to run without USIM card")
		panic(err)
	}

	// Set the card reader (adjust this to match your reader)
	d.<PERSON>er("Alcor Link AK9563 00 00")

	// Create transmitter
	transmitter, err := driver.NewTransmitter(logger, d)
	if err != nil {
		logger.Error("Failed to create transmitter", "error", err)
		panic(err)
	}
	defer transmitter.Close()

	// Initialize USIM
	usimCard, err := usim.New(transmitter, logger)
	if err != nil {
		logger.Error("Failed to initialize USIM", "error", err)
		panic(err)
	}

	// Get USIM information
	imsi, err := usimCard.GetIMSI()
	if err != nil {
		logger.Error("Failed to get IMSI", "error", err)
		panic(err)
	}

	iccid, err := usimCard.GetICCID()
	if err != nil {
		logger.Error("Failed to get ICCID", "error", err)
		panic(err)
	}

	logger.Info("USIM Information",
		"imsi", imsi.String(),
		"mcc", imsi.MCC(),
		"mnc", imsi.MNC(),
		"iccid", iccid.String())

	// Create IMEI (you should replace this with actual device IMEI)
	imei, err := usim.NewIMEI("3566564213052700")
	if err != nil {
		logger.Error("Failed to create IMEI", "error", err)
		panic(err)
	}

	// Run VoWiFi connection
	if err := runVoWiFi(usimCard, imei, logger); err != nil {
		logger.Error("VoWiFi connection failed", "error", err)
		panic(err)
	}
}

// runVoWiFi establishes VoWiFi connection using SWu IKEv2
func runVoWiFi(usimCard *usim.USIM, imei usim.IMEI, logger *slog.Logger) error {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Create IKEv2 client
	ikev2, err := swu.NewIKEv2(ctx, usimCard, imei, "", logger)
	if err != nil {
		return fmt.Errorf("failed to create IKEv2 client: %w", err)
	}
	defer ikev2.Close()

	logger.Info("Starting VoWiFi connection...")

	// Start connection in a goroutine
	connectionDone := make(chan error, 1)
	go func() {
		connectionDone <- ikev2.Listen()
	}()

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for connection result or signal
	select {
	case err := <-connectionDone:
		if err != nil {
			return fmt.Errorf("VoWiFi connection failed: %w", err)
		}

		// Connection successful, get assigned addresses
		clientAddr := ikev2.GetClientAddress()
		pcscfAddr := ikev2.GetPCSCFAddress()

		logger.Info("VoWiFi connection established successfully",
			"client_address", clientAddr,
			"pcscf_address", pcscfAddr)

		// Keep connection alive until interrupted
		logger.Info("VoWiFi tunnel is active. Press Ctrl+C to disconnect.")
		<-sigChan
		logger.Info("Received shutdown signal, disconnecting...")

	case <-sigChan:
		logger.Info("Received shutdown signal during connection setup")
		cancel()

	case <-ctx.Done():
		return fmt.Errorf("connection timeout: %w", ctx.Err())
	}

	return nil
}

// runDemo demonstrates the SWu client functionality without requiring a real USIM card
func runDemo(logger *slog.Logger) {
	logger.Info("=== VoWiFi SWu Client Demo ===")
	logger.Info("This demo shows the SWu IKEv2 client functionality without requiring a real USIM card")

	// Demo USIM parameters
	demoIMSI := "123456789012345"
	demoMCC := "123"
	demoMNC := "456"
	demoIMEI := "3566564213052700"

	logger.Info("Demo USIM Parameters",
		"imsi", demoIMSI,
		"mcc", demoMCC,
		"mnc", demoMNC,
		"imei", demoIMEI)

	// Construct demo addresses
	epdgAddr := fmt.Sprintf("epdg.epc.mnc%s.mcc%s.pub.3gppnetwork.org", demoMNC, demoMCC)
	nai := fmt.Sprintf("<EMAIL>%s.mcc%s.3gppnetwork.org", demoIMSI, demoMNC, demoMCC)

	logger.Info("Demo Network Configuration",
		"epdg_address", epdgAddr,
		"nai", nai)

	// Demo protocol flow
	logger.Info("=== SWu IKEv2 Protocol Flow Demo ===")

	// Phase 1: IKE_SA_INIT
	logger.Info("Phase 1: IKE_SA_INIT Exchange")
	logger.Info("  → Client sends: SA, KE, Nonce, NAT-D payloads")
	logger.Info("  ← Server responds: SA, KE, Nonce, NAT-D payloads")
	logger.Info("  ✓ DH shared secret computed, IKE keys derived")

	time.Sleep(500 * time.Millisecond)

	// Phase 2: IKE_AUTH (EAP-AKA)
	logger.Info("Phase 2: IKE_AUTH Exchange (EAP-AKA)")
	logger.Info("  → Client sends: ID payload with NAI")
	logger.Info("  ← Server responds: EAP-AKA Challenge (RAND, AUTN)")
	logger.Info("  → Client performs USIM authentication")
	logger.Info("  → Client sends: EAP-AKA Response (RES)")
	logger.Info("  ← Server sends: EAP Success")

	time.Sleep(500 * time.Millisecond)

	// Phase 3: Final IKE_AUTH
	logger.Info("Phase 3: Final IKE_AUTH Exchange")
	logger.Info("  → Client sends: AUTH payload")
	logger.Info("  ← Server responds: AUTH, SA, CP payloads")
	logger.Info("  ✓ Configuration received: Client IP, P-CSCF IP, DNS")

	time.Sleep(500 * time.Millisecond)

	// Phase 4: Tunnel established
	logger.Info("Phase 4: IPSec Tunnel Established")
	logger.Info("  ✓ ESP tunnel mode with encryption and integrity")
	logger.Info("  ✓ Client can now communicate with IMS network")

	// Demo addresses
	demoClientIP := "********00"
	demoPCSCFIP := "********"

	logger.Info("Demo Connection Result",
		"client_address", demoClientIP,
		"pcscf_address", demoPCSCFIP,
		"status", "connected")

	logger.Info("=== Demo Complete ===")
	logger.Info("In a real deployment:")
	logger.Info("  1. Connect USIM card reader")
	logger.Info("  2. Update reader name in main.go")
	logger.Info("  3. Update IMEI in main.go")
	logger.Info("  4. Run: sudo ./vowifi")
	logger.Info("  5. The client will establish a real VoWiFi tunnel")
}
