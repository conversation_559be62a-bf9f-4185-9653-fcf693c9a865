package main

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/damonto/vowifi/driver"
	"github.com/damonto/vowifi/driver/ccid"
	"github.com/damonto/vowifi/swu"
	"github.com/damonto/vowifi/usim"
)

func main() {
	// Set up logging
	slog.SetLogLoggerLevel(slog.LevelDebug)
	logger := slog.Default()

	// Initialize CCID driver
	d, err := ccid.New()
	if err != nil {
		logger.Error("Failed to create CCID driver", "error", err)
		panic(err)
	}

	// Set the card reader (adjust this to match your reader)
	d.<PERSON><PERSON><PERSON><PERSON>("Alcor Link AK9563 00 00")

	// Create transmitter
	transmitter, err := driver.NewTransmitter(logger, d)
	if err != nil {
		logger.Error("Failed to create transmitter", "error", err)
		panic(err)
	}
	defer transmitter.Close()

	// Initialize USIM
	usimCard, err := usim.New(transmitter, logger)
	if err != nil {
		logger.Error("Failed to initialize USIM", "error", err)
		panic(err)
	}

	// Get USIM information
	imsi, err := usimCard.GetIMSI()
	if err != nil {
		logger.Error("Failed to get IMSI", "error", err)
		panic(err)
	}

	iccid, err := usimCard.GetICCID()
	if err != nil {
		logger.Error("Failed to get ICCID", "error", err)
		panic(err)
	}

	logger.Info("USIM Information",
		"imsi", imsi.String(),
		"mcc", imsi.MCC(),
		"mnc", imsi.MNC(),
		"iccid", iccid.String())

	// Create IMEI (you should replace this with actual device IMEI)
	imei, err := usim.NewIMEI("3566564213052700")
	if err != nil {
		logger.Error("Failed to create IMEI", "error", err)
		panic(err)
	}

	// Run VoWiFi connection
	if err := runVoWiFi(usimCard, imei, logger); err != nil {
		logger.Error("VoWiFi connection failed", "error", err)
		panic(err)
	}
}

// runVoWiFi establishes VoWiFi connection using SWu IKEv2
func runVoWiFi(usimCard *usim.USIM, imei usim.IMEI, logger *slog.Logger) error {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Create IKEv2 client
	ikev2, err := swu.NewIKEv2(ctx, usimCard, imei, "", logger)
	if err != nil {
		return fmt.Errorf("failed to create IKEv2 client: %w", err)
	}
	defer ikev2.Close()

	logger.Info("Starting VoWiFi connection...")

	// Start connection in a goroutine
	connectionDone := make(chan error, 1)
	go func() {
		connectionDone <- ikev2.Listen()
	}()

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for connection result or signal
	select {
	case err := <-connectionDone:
		if err != nil {
			return fmt.Errorf("VoWiFi connection failed: %w", err)
		}

		// Connection successful, get assigned addresses
		clientAddr := ikev2.GetClientAddress()
		pcscfAddr := ikev2.GetPCSCFAddress()

		logger.Info("VoWiFi connection established successfully",
			"client_address", clientAddr,
			"pcscf_address", pcscfAddr)

		// Keep connection alive until interrupted
		logger.Info("VoWiFi tunnel is active. Press Ctrl+C to disconnect.")
		<-sigChan
		logger.Info("Received shutdown signal, disconnecting...")

	case <-sigChan:
		logger.Info("Received shutdown signal during connection setup")
		cancel()

	case <-ctx.Done():
		return fmt.Errorf("connection timeout: %w", ctx.Err())
	}

	return nil
}
