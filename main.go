package main

import (
	"context"
	"encoding/hex"
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/damonto/vowifi/driver"
	"github.com/damonto/vowifi/driver/ccid"
	"github.com/damonto/vowifi/swu"
	"github.com/damonto/vowifi/usim"
)

func testKeyDerivation(logger *slog.Logger) {
	// Test key derivation with fixed values
	crypto := swu.NewCryptoHelper(logger)
	crypto.SetCryptoMode(1, 1, 12, 128) // PRF=1 (HMAC-MD5), INTEG=1 (HMAC-MD5-96), ENCR=12 (AES-CBC-128), ENCR_KEYLEN=128

	// Set fixed test values
	dhKey, _ := hex.DecodeString("0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef")
	nonceI, _ := hex.DecodeString("0123456789abcdef0123456789abcdef")
	nonceR, _ := hex.DecodeString("fedcba9876543210fedcba9876543210")
	spiI := uint64(0x1234567890abcdef)
	spiR := uint64(0xfedcba0987654000) // PHP has precision issues with large hex values

	// Set values in crypto helper
	crypto.SetSPI(spiI, spiR)
	crypto.SetNonce(nonceI, nonceR)
	crypto.SetDHSharedKeyForTest(dhKey)

	// Derive keys
	err := crypto.DeriveKeys()
	if err != nil {
		logger.Error("Error deriving keys", "error", err)
		return
	}

	// Get derived keys
	keys := crypto.GetKeysForTest()

	logger.Info("=== KEY DERIVATION TEST ===")
	logger.Info("DH_KEY", "value", fmt.Sprintf("%x", dhKey))
	logger.Info("NONCES", "value", fmt.Sprintf("%x", append(nonceI, nonceR...)))
	logger.Info("SKEYSEED", "value", fmt.Sprintf("%x", keys["skeyseed"]))
	logger.Info("SK_D", "value", fmt.Sprintf("%x", keys["sk_d"]))
	logger.Info("SK_AI", "value", fmt.Sprintf("%x", keys["sk_ai"]))
	logger.Info("SK_AR", "value", fmt.Sprintf("%x", keys["sk_ar"]))
	logger.Info("SK_EI", "value", fmt.Sprintf("%x", keys["sk_ei"]))
	logger.Info("SK_ER", "value", fmt.Sprintf("%x", keys["sk_er"]))
}

func main() {
	// Set up logging
	slog.SetLogLoggerLevel(slog.LevelDebug)
	logger := slog.Default()

	// Run key derivation test
	if len(os.Args) > 1 && os.Args[1] == "test" {
		testKeyDerivation(logger)
		return
	}

	// Initialize CCID driver
	d, err := ccid.New()
	if err != nil {
		logger.Error("Failed to create CCID driver", "error", err)
		panic(err)
	}

	// Set the card reader (adjust this to match your reader)
	d.SetReader("Alcor Link AK9563 00 00")

	// Create transmitter
	transmitter, err := driver.NewTransmitter(logger, d)
	if err != nil {
		logger.Error("Failed to create transmitter", "error", err)
		panic(err)
	}
	defer transmitter.Close()

	// Initialize USIM
	usimCard, err := usim.New(transmitter, logger)
	if err != nil {
		logger.Error("Failed to initialize USIM", "error", err)
		panic(err)
	}

	// Get USIM information
	imsi, err := usimCard.GetIMSI()
	if err != nil {
		logger.Error("Failed to get IMSI", "error", err)
		panic(err)
	}

	iccid, err := usimCard.GetICCID()
	if err != nil {
		logger.Error("Failed to get ICCID", "error", err)
		panic(err)
	}

	logger.Info("USIM Information",
		"imsi", imsi.String(),
		"mcc", imsi.MCC(),
		"mnc", imsi.MNC(),
		"iccid", iccid.String())

	// Create IMEI (using the same IMEI as PHP implementation for consistency)
	imei, err := usim.NewIMEI("356656421305276")
	if err != nil {
		logger.Error("Failed to create IMEI", "error", err)
		panic(err)
	}

	// Run VoWiFi connection
	if err := runVoWiFi(usimCard, imei, logger); err != nil {
		logger.Error("VoWiFi connection failed", "error", err)
		panic(err)
	}
}

// runVoWiFi establishes VoWiFi connection using SWu IKEv2
func runVoWiFi(usimCard *usim.USIM, imei usim.IMEI, logger *slog.Logger) error {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Create IKEv2 client
	ikev2, err := swu.NewIKEv2(ctx, usimCard, imei, "", logger)
	if err != nil {
		return fmt.Errorf("failed to create IKEv2 client: %w", err)
	}
	defer ikev2.Close()

	logger.Info("========== STARTING VOWIFI CONNECTION ==========")
	logger.Info("Starting VoWiFi SWu IKEv2 connection...")

	// Start connection in a goroutine
	connectionDone := make(chan error, 1)
	go func() {
		connectionDone <- ikev2.Listen()
	}()

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for connection result or signal
	select {
	case err := <-connectionDone:
		if err != nil {
			return fmt.Errorf("VoWiFi connection failed: %w", err)
		}

		// Connection successful, get assigned addresses
		clientAddr := ikev2.GetClientAddress()
		pcscfAddr := ikev2.GetPCSCFAddress()

		logger.Info("VoWiFi connection established successfully",
			"client_address", clientAddr,
			"pcscf_address", pcscfAddr)

		// Keep connection alive until interrupted
		logger.Info("VoWiFi tunnel is active. Press Ctrl+C to disconnect.")
		<-sigChan
		logger.Info("Received shutdown signal, disconnecting...")

	case <-sigChan:
		logger.Info("Received shutdown signal during connection setup")
		cancel()

	case <-ctx.Done():
		return fmt.Errorf("connection timeout: %w", ctx.Err())
	}

	return nil
}
