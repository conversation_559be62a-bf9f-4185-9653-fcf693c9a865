#!/bin/bash

# VoWiFi客户端启动脚本
# 确保项目能正常运行

set -e

echo "=== VoWiFi客户端启动脚本 ==="
echo

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "错误: 需要root权限运行此脚本"
    echo "请使用: sudo ./run.sh"
    exit 1
fi

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go环境"
    echo "请安装Go 1.21或更高版本"
    exit 1
fi

echo "✅ Go环境检查通过: $(go version)"

# 编译项目
echo "🔨 编译项目..."
if ! go build -o vowifi .; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"

# 检查可执行文件
if [ ! -f "./vowifi" ]; then
    echo "❌ 可执行文件不存在"
    exit 1
fi

echo "✅ 可执行文件检查通过"

# 运行测试
echo "🧪 运行测试..."
if ! go test ./swu -v; then
    echo "❌ 测试失败"
    exit 1
fi

echo "✅ 测试通过"

# 检查读卡器（可选）
echo "🔍 检查PCSC服务..."
if command -v pcsc_scan &> /dev/null; then
    echo "✅ PCSC工具可用"
    echo "提示: 可以运行 'pcsc_scan' 检查读卡器连接"
else
    echo "⚠️  PCSC工具未安装，可能影响USIM卡读取"
    echo "建议安装: apt-get install pcsc-tools"
fi

echo
echo "=== 配置检查 ==="
echo "请确认以下配置："
echo "1. 读卡器名称 (main.go:31行): Alcor Link AK9563 00 00"
echo "2. 设备IMEI (main.go:68行): 356656421305276"
echo "3. USIM卡已正确插入读卡器"
echo

# 显示使用说明
echo "=== 使用说明 ==="
echo "1. 确保USIM卡已插入读卡器"
echo "2. 检查网络连接（需要访问运营商ePDG服务器）"
echo "3. 运行客户端："
echo "   sudo ./vowifi"
echo
echo "4. 预期输出："
echo "   - USIM信息读取成功"
echo "   - ePDG地址解析成功"
echo "   - IKEv2认证流程完成"
echo "   - 获得客户端IP和P-CSCF地址"
echo

# 询问是否立即运行
read -p "是否立即运行VoWiFi客户端? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 启动VoWiFi客户端..."
    echo "按Ctrl+C退出"
    echo
    ./vowifi
else
    echo "✅ 准备完成，可以手动运行: sudo ./vowifi"
fi
