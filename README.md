# VoWiFi Client

A complete VoWiFi (Voice over WiFi) client implementation in Go that supports SWu IKEv2 authentication and tunnel establishment.

## Features

- **Complete SWu IKEv2 Implementation**: Full protocol support for VoWiFi authentication
- **USIM Integration**: Automatic parameter retrieval from USIM card
- **ePDG Connection**: Automatic ePDG address construction and connection
- **EAP-AKA Authentication**: 3GPP authentication using USIM credentials
- **IPSec Tunnel**: ESP tunnel mode with encryption and integrity protection
- **Modern Go Implementation**: Uses latest Go features and best practices
- **Comprehensive Logging**: Structured logging with slog for debugging

## Architecture

The implementation consists of several key components:

- **USIM Module**: Smart card interface for authentication parameters
- **SWu Client**: IKEv2 protocol implementation for VoWiFi
- **Network Layer**: UDP connection management with NAT-T support
- **Crypto Helper**: Cryptographic operations (DH, encryption, integrity)
- **Packet Handling**: Complete IKEv2 packet marshaling/unmarshaling

## Prerequisites

- Go 1.21 or later
- USIM card and compatible card reader
- Network access to ePDG server

## Building

```bash
# Clone the repository
git clone <repository-url>
cd vowifi

# Build the project
go build -o vowifi .
```

## Usage

### Basic Usage

1. **Connect your USIM card reader**
2. **Update the card reader name** in `main.go` if needed:
   ```go
   d.SetReader("Alcor Link AK9563 00 00")  // Change this to match your reader
   ```
3. **Update the IMEI** in `main.go`:
   ```go
   imei, err := usim.NewIMEI("3566564213052700")  // Replace with your device IMEI
   ```
4. **Run the client**:
   ```bash
   sudo ./vowifi
   ```

### Command Line Options

The client currently uses hardcoded configuration. Future versions will support command-line options.

### Configuration

The client automatically:
- Retrieves IMSI, MCC, MNC from USIM card
- Constructs ePDG address: `epdg.epc.mnc{MNC}.mcc{MCC}.pub.3gppnetwork.org`
- Generates NAI: `0{IMSI}@nai.epc.mnc{MNC}.mcc{MCC}.3gppnetwork.org`
- Performs 3GPP authentication using USIM parameters

## Protocol Flow

1. **USIM Initialization**
   - Read IMSI, ICCID from USIM card
   - Extract MCC, MNC for network identification

2. **IKE_SA_INIT Exchange**
   - Client sends SA, KE, Nonce, NAT-D payloads
   - Server responds with SA, KE, Nonce, NAT-D payloads
   - DH shared secret computed, IKE keys derived

3. **IKE_AUTH Exchange (EAP-AKA)**
   - Client sends ID payload with NAI
   - Server responds with EAP-AKA Challenge (RAND, AUTN)
   - Client performs USIM authentication
   - Client sends EAP-AKA Response (RES)
   - Server sends EAP Success

4. **Final IKE_AUTH Exchange**
   - Client sends AUTH payload
   - Server responds with AUTH, SA, CP payloads
   - Configuration includes client IP, P-CSCF IP, DNS servers

5. **IPSec Tunnel Established**
   - ESP tunnel mode with encryption and integrity
   - Client can communicate with IMS network

## Logging

The client provides detailed logging at different levels:

- **DEBUG**: Detailed protocol messages and crypto operations
- **INFO**: Connection status and important events
- **ERROR**: Error conditions and failures

Example output:
```
INFO [SWuClient] created imei=3566564213052700 imsi=123456789012345 nai=<EMAIL> epdg_address=epdg.epc.mnc001.mcc123.pub.3gppnetwork.org
INFO Starting VoWiFi connection...
INFO [SWuClient] initializing SWu connection...
DEBUG [CryptoHelper] DH key pair generated group=2
INFO VoWiFi connection established successfully client_address=********00 pcscf_address=********
INFO VoWiFi tunnel is active. Press Ctrl+C to disconnect.
```

## Troubleshooting

### Common Issues

1. **Card Reader Not Found**
   - Check card reader connection
   - Update reader name in `main.go`
   - Verify CCID driver support

2. **USIM Authentication Failed**
   - Ensure USIM card is properly inserted
   - Check USIM card compatibility
   - Verify network operator support

3. **ePDG Connection Failed**
   - Check network connectivity
   - Verify ePDG address construction
   - Check firewall settings (UDP ports 500, 4500)

4. **IKEv2 Authentication Failed**
   - Verify USIM authentication parameters
   - Check IMEI validity
   - Ensure proper NAI format

### Debug Mode

For detailed debugging, the client runs with DEBUG level logging by default. This shows:
- IKEv2 packet details
- Cryptographic operations
- Network events
- USIM interactions

## Testing

Run the test suite:

```bash
# Run all tests
go test ./...

# Run SWu module tests specifically
go test ./swu -v

# Run examples
go test ./swu -run Example -v
```

## Development

### Project Structure

```
├── main.go              # Main application entry point
├── swu/                 # SWu IKEv2 implementation
│   ├── client.go        # Main SWu client
│   ├── handlers.go      # IKEv2 response handlers
│   ├── crypto.go        # Cryptographic operations
│   ├── packet.go        # IKEv2 packet handling
│   ├── payload.go       # IKEv2 payload types
│   ├── network.go       # Network layer
│   ├── transaction.go   # State management
│   └── ikev2.go         # High-level IKEv2 interface
├── usim/                # USIM card interface
├── driver/              # Card reader drivers
└── apdu/                # APDU command handling
```

### Adding Features

1. **New Payload Types**: Add to `swu/payload.go`
2. **Crypto Algorithms**: Extend `swu/crypto.go`
3. **Network Protocols**: Modify `swu/network.go`
4. **Authentication Methods**: Update `swu/handlers.go`

## References

- 3GPP TS 24.302: Access to the 3GPP Evolved Packet Core (EPC) via non-3GPP access networks
- RFC 7296: Internet Key Exchange Protocol Version 2 (IKEv2)
- RFC 4187: Extensible Authentication Protocol Method for 3rd Generation Authentication and Key Agreement (EAP-AKA)

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs for error details
3. Open an issue with detailed information
4. Include log output and system information
