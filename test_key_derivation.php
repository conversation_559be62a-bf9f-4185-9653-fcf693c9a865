<?php

// Test key derivation to compare with Go implementation
function prfHmac($data, $key)
{
    return hash_hmac('md5', $data, $key, true);
}

function prfPlus($KEY, $STREAM, $SIZE)
{
    $ret = "";
    $lastsegment = "";
    for ($i = 1; strlen($ret) < $SIZE; $i++) {
        $data = $lastsegment . $STREAM . pack('C', $i);
        $lastsegment = prfHmac($data, $KEY);

        // Debug first iteration
        if ($i == 1) {
            echo "prfPlus first iteration:\n";
            echo "KEY: " . bin2hex($KEY) . "\n";
            echo "STREAM: " . bin2hex($STREAM) . "\n";
            echo "DATA: " . bin2hex($data) . "\n";
            echo "RESULT: " . bin2hex($lastsegment) . "\n";
        }

        $ret .= $lastsegment;
    }
    return $ret;
}

// Test with fixed values
$dh_key = hex2bin("0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef");
$nonce_i = hex2bin("0123456789abcdef0123456789abcdef");
$nonce_r = hex2bin("fedcba9876543210fedcba9876543210");
$spi_i = 0x1234567890abcdef;
$spi_r = 0xfedcba0987654321;

echo "SPI_I: " . sprintf("%016x", $spi_i) . "\n";
echo "SPI_R: " . sprintf("%016x", $spi_r) . "\n";

$nonces = $nonce_i . $nonce_r;
$skeyseed = prfHmac($dh_key, $nonces);
$stream = $nonces . pack('J', $spi_i) . pack('J', $spi_r);

echo "DH_KEY: " . bin2hex($dh_key) . "\n";
echo "NONCES: " . bin2hex($nonces) . "\n";
echo "SKEYSEED: " . bin2hex($skeyseed) . "\n";
echo "STREAM: " . bin2hex($stream) . "\n";

// Derive keys
$key_length_total = (3 * 16) + (2 * 16) + (2 * 16); // 3*prf + 2*integ + 2*encr (all 16 bytes for MD5/AES-128)
$key_stream = prfPlus($skeyseed, $stream, $key_length_total);

echo "KEY_STREAM: " . bin2hex($key_stream) . "\n";

// Extract individual keys
$offset = 0;
$sk_d = substr($key_stream, $offset, 16);
$offset += 16;
$sk_ai = substr($key_stream, $offset, 16);
$offset += 16;
$sk_ar = substr($key_stream, $offset, 16);
$offset += 16;
$sk_ei = substr($key_stream, $offset, 16);
$offset += 16;
$sk_er = substr($key_stream, $offset, 16);
$offset += 16;

echo "SK_D: " . bin2hex($sk_d) . "\n";
echo "SK_AI: " . bin2hex($sk_ai) . "\n";
echo "SK_AR: " . bin2hex($sk_ar) . "\n";
echo "SK_EI: " . bin2hex($sk_ei) . "\n";
echo "SK_ER: " . bin2hex($sk_er) . "\n";

?>

