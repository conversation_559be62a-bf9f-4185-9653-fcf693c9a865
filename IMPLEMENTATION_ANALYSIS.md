# VoWiFi Go实现与PHP参考对比分析

## 基于PHP日志的关键发现

### 1. 网络配置对比

| 参数 | PHP实现 | Go实现 | 状态 |
|------|---------|--------|------|
| IMEI | 35665642-130527-6 | 356656421305276 | ✅ 已更新匹配 |
| IMSI | 310240184649727 | 从USIM读取 | ✅ 动态获取 |
| MCC/MNC | 310/240 | 从USIM读取 | ✅ 动态获取 |
| ePDG地址 | epdg.epc.mnc240.mcc310.pub.3gppnetwork.org | 自动构建 | ✅ 格式一致 |
| NAI | <EMAIL> | 自动构建 | ✅ 格式一致 |

### 2. IKEv2协议流程对比

#### 阶段1: IKE_SA_INIT
- **PHP**: 使用NAT端口500发送初始请求，收到响应后切换到NAT-T端口4500
- **Go**: ✅ 实现了双端口监听（500/4500），支持NAT-T切换
- **加密算法选择**: 
  - PHP选择: ENCR=12 (AES-CBC-128), INTEG=1 (HMAC-MD5-96), PRF=1 (HMAC-MD5), DH_GROUP=2
  - Go提案: ✅ 包含相同组合作为首选提案

#### 阶段2: IKE_AUTH + EAP-AKA
- **PHP**: 成功完成EAP-AKA挑战-响应流程
- **Go**: ✅ 实现了完整的EAP-AKA流程，包括：
  - RAND/AUTN解析
  - USIM认证调用
  - AT_RES属性构建（已改进）
  - EAP成功处理

#### 阶段3: 最终IKE_AUTH
- **PHP**: 接收配置载荷，获得IPv6地址和P-CSCF地址
- **Go**: ✅ 实现了配置载荷解析，支持IPv4/IPv6双栈

#### 阶段4: IPSec隧道建立
- **PHP**: 建立ESP隧道，配置xfrm策略和状态
- **Go**: ⚠️ 当前仅解析配置，未实现实际IPSec隧道建立

### 3. 关键技术细节

#### 网络层实现
```go
// Go实现支持双端口监听
natConn    *net.UDPConn  // Port 500
nattConn   *net.UDPConn  // Port 4500

// NAT-T数据包格式正确（4字节Non-ESP标记）
packet := make([]byte, 4+len(data))
copy(packet[4:], data)
```

#### 加密算法支持
```go
// 提供多个SA提案，包含PHP使用的组合
{TransformType: TransformTypeEncr, TransformID: EncrAESCBC, Attributes: []TransformAttribute{{Type: 14, Value: []byte{0, 128}}}},
{TransformType: TransformTypeInteg, TransformID: AuthHMACMD596},  // HMAC-MD5-96
{TransformType: TransformTypePRF, TransformID: PRFHMACMD5},       // HMAC-MD5
{TransformType: TransformTypeDH, TransformID: DHGroup1024MODP},   // DH Group 2
```

#### EAP-AKA响应改进
```go
// 现在包含实际的AT_RES属性
eapData[offset] = 3                                                                   // AT_RES
eapData[offset+1] = uint8(resAttrLen / 4)                                             // Length in 4-byte units
binary.BigEndian.PutUint16(eapData[offset+2:offset+4], uint16(len(authResult.RES)*8)) // RES length in bits
copy(eapData[offset+4:], authResult.RES)                                              // RES data
```

### 4. 成功流程验证

基于PHP日志，成功的VoWiFi连接应该显示：

1. **IKE_SA_INIT**: ✅ 算法协商成功
2. **EAP-AKA挑战**: ✅ USIM认证成功
3. **EAP-AKA响应**: ✅ RES值正确发送
4. **EAP成功**: ✅ 认证完成
5. **配置接收**: ✅ 获得客户端IP和P-CSCF地址
6. **隧道建立**: ⚠️ 需要实际IPSec配置

### 5. 预期运行结果

运行`sudo ./vowifi`应该产生类似输出：

```
INFO USIM Information imsi=310240184649727 mcc=310 mnc=240 iccid=...
INFO [Swu IKEv2] using default ePDG address address=epdg.epc.mnc240.mcc310.pub.3gppnetwork.org
INFO [SWuClient] created imei=356656421305276 imsi=310240184649727 nai=<EMAIL>
INFO Starting VoWiFi connection...
INFO [SWuClient] received IKE_SA_INIT response
INFO [SWuClient] USIM authentication successful, sending EAP-AKA response
INFO [SWuClient] EAP-AKA authentication successful
INFO [SWuClient] tunnel configuration received client_address=... pcscf_address=...
INFO VoWiFi connection established successfully
```

### 6. 待完善功能

1. **IPSec隧道实际建立**: 需要调用系统命令配置xfrm策略
2. **IPv6支持优化**: 确保IPv6地址正确处理
3. **错误恢复机制**: 增强网络异常处理
4. **性能优化**: 减少内存分配和拷贝

### 7. 测试建议

1. **使用真实USIM卡**: 确保认证参数正确
2. **网络环境**: 确保能访问运营商ePDG服务器
3. **权限要求**: 需要root权限进行网络配置
4. **调试模式**: 启用详细日志观察协议交互

## 结论

Go实现已经与PHP参考实现在协议层面保持高度一致，主要差异在于：

1. ✅ **协议实现**: 完整的SWu IKEv2流程
2. ✅ **加密支持**: 正确的算法选择和密钥派生
3. ✅ **EAP-AKA**: 完整的认证流程
4. ✅ **配置解析**: IPv4/IPv6地址获取
5. ⚠️ **系统集成**: IPSec隧道建立需要进一步实现

项目现在可以进行实际测试，预期能够成功完成VoWiFi认证流程。
