2025/06/28 19:21:45 DEBUG [APDU] sending command=00A40004023F00
[DEBUG] APDU response length: 2, status: 612A
[DEBUG] APDU response data: 612a
[DEBUG] APDU response length: 44, status: 9000
[DEBUG] APDU response data: 62288202782183023f00a50b80017183030dff368701018a01058b032f0602c60990014083010183010a9000
2025/06/28 19:21:45 DEBUG [APDU] received response=62288202782183023F00A50B80017183030DFF368701018A01058B032F0602C60990014083010183010A
2025/06/28 19:21:45 DEBUG [APDU] sending command=00A40004022F00
[DEBUG] APDU response length: 2, status: 611C
[DEBUG] APDU response data: 611c
[DEBUG] APDU response length: 30, status: 9000
[DEBUG] APDU response data: 621a8205422100260483022f008a01058b032f060c800200988801f09000
2025/06/28 19:21:45 DEBUG [APDU] received response=621A8205422100260483022F008A01058B032F060C800200988801F0
2025/06/28 19:21:45 DEBUG [APDU] sending command=00B2010426
[DEBUG] APDU response length: 40, status: 9000
[DEBUG] APDU response data: 61184f10a0000000871002ffffffff890619000050045553494dffffffffffffffffffffffff9000
2025/06/28 19:21:45 DEBUG [APDU] received response=61184F10A0000000871002FFFFFFFF890619000050045553494DFFFFFFFFFFFFFFFFFFFFFFFF
2025/06/28 19:21:45 DEBUG [APDU] sending command=00A4040410A0000000871002FFFFFFFF8906190000
[DEBUG] APDU response length: 2, status: 612B
[DEBUG] APDU response data: 612b
[DEBUG] APDU response length: 45, status: 9000
[DEBUG] APDU response data: 6229820278218410a0000000871002ffffffff89061900008a01058b032f0607c6099001408301018301819000
2025/06/28 19:21:45 DEBUG [APDU] received response=6229820278218410A0000000871002FFFFFFFF89061900008A01058B032F0607C609900140830101830181
2025/06/28 19:21:45 DEBUG [APDU] sending command=00A40004026F07
[DEBUG] APDU response length: 2, status: 6119
[DEBUG] APDU response data: 6119
[DEBUG] APDU response length: 27, status: 9000
[DEBUG] APDU response data: 62178202412183026f078a01058b036f0617800200098801389000
2025/06/28 19:21:45 DEBUG [APDU] received response=62178202412183026F078A01058B036F061780020009880138
2025/06/28 19:21:45 DEBUG [APDU] sending command=00B0000009
[DEBUG] APDU response length: 11, status: 9000
[DEBUG] APDU response data: 0839014210484679729000
2025/06/28 19:21:45 DEBUG [APDU] received response=083901421048467972
2025/06/28 19:21:45 DEBUG [APDU] sending command=00A4040410A0000000871002FFFFFFFF8906190000
[DEBUG] APDU response length: 2, status: 612B
[DEBUG] APDU response data: 612b
[DEBUG] APDU response length: 45, status: 9000
[DEBUG] APDU response data: 6229820278218410a0000000871002ffffffff89061900008a01058b032f0607c6099001408301018301819000
2025/06/28 19:21:45 DEBUG [APDU] received response=6229820278218410A0000000871002FFFFFFFF89061900008A01058B032F0607C609900140830101830181
2025/06/28 19:21:45 DEBUG [APDU] sending command=00A40804022FE2
[DEBUG] APDU response length: 2, status: 6119
[DEBUG] APDU response data: 6119
[DEBUG] APDU response length: 27, status: 9000
[DEBUG] APDU response data: 62178202412183022fe28a01058b032f060e8002000a8801109000
2025/06/28 19:21:45 DEBUG [APDU] received response=62178202412183022FE28A01058B032F060E8002000A880110
2025/06/28 19:21:45 DEBUG [APDU] sending command=00B000000A
[DEBUG] APDU response length: 12, status: 9000
[DEBUG] APDU response data: 981042107841467972f29000
2025/06/28 19:21:45 DEBUG [APDU] received response=981042107841467972F2
2025/06/28 19:21:45 INFO USIM Information imsi=310240184649727 mcc=310 mnc=240 iccid=8901240187146497272
2025/06/28 19:21:45 DEBUG [APDU] sending command=00A4040410A0000000871002FFFFFFFF8906190000
[DEBUG] APDU response length: 2, status: 612B
[DEBUG] APDU response data: 612b
[DEBUG] APDU response length: 45, status: 9000
[DEBUG] APDU response data: 6229820278218410a0000000871002ffffffff89061900008a01058b032f0607c6099001408301018301819000
2025/06/28 19:21:45 DEBUG [APDU] received response=6229820278218410A0000000871002FFFFFFFF89061900008A01058B032F0607C609900140830101830181
2025/06/28 19:21:45 DEBUG [APDU] sending command=00A40004026F07
[DEBUG] APDU response length: 2, status: 6119
[DEBUG] APDU response data: 6119
[DEBUG] APDU response length: 27, status: 9000
[DEBUG] APDU response data: 62178202412183026f078a01058b036f0617800200098801389000
2025/06/28 19:21:45 DEBUG [APDU] received response=62178202412183026F078A01058B036F061780020009880138
2025/06/28 19:21:45 DEBUG [APDU] sending command=00B0000009
[DEBUG] APDU response length: 11, status: 9000
[DEBUG] APDU response data: 0839014210484679729000
2025/06/28 19:21:45 DEBUG [APDU] received response=083901421048467972
2025/06/28 19:21:45 INFO [Swu IKEv2] using default ePDG address address=epdg.epc.mnc240.mcc310.pub.3gppnetwork.org
2025/06/28 19:21:45 DEBUG [APDU] sending command=00A4040410A0000000871002FFFFFFFF8906190000
[DEBUG] APDU response length: 2, status: 612B
[DEBUG] APDU response data: 612b
[DEBUG] APDU response length: 45, status: 9000
[DEBUG] APDU response data: 6229820278218410a0000000871002ffffffff89061900008a01058b032f0607c6099001408301018301819000
2025/06/28 19:21:45 DEBUG [APDU] received response=6229820278218410A0000000871002FFFFFFFF89061900008A01058B032F0607C609900140830101830181
2025/06/28 19:21:45 DEBUG [APDU] sending command=00A40004026F07
[DEBUG] APDU response length: 2, status: 6119
[DEBUG] APDU response data: 6119
[DEBUG] APDU response length: 27, status: 9000
[DEBUG] APDU response data: 62178202412183026f078a01058b036f0617800200098801389000
2025/06/28 19:21:45 DEBUG [APDU] received response=62178202412183026F078A01058B036F061780020009880138
2025/06/28 19:21:45 DEBUG [APDU] sending command=00B0000009
[DEBUG] APDU response length: 11, status: 9000
[DEBUG] APDU response data: 0839014210484679729000
2025/06/28 19:21:45 DEBUG [APDU] received response=083901421048467972
2025/06/28 19:21:45 INFO [SWuClient] using provided ePDG address address=epdg.epc.mnc240.mcc310.pub.3gppnetwork.org
2025/06/28 19:21:45 DEBUG [CryptoHelper] DH key pair generated group=2
2025/06/28 19:21:45 INFO [Transaction] created imei=356656421305276 nai=<EMAIL> spi_initiator=0xb17073c6bf6e20e4 esp_spi_initiator=0xdfdeb992
2025/06/28 19:21:45 INFO [SWuClient] created imei=356656421305276 imsi=310240184649727 nai=<EMAIL> epdg_address=epdg.epc.mnc240.mcc310.pub.3gppnetwork.org
2025/06/28 19:21:45 INFO [Swu IKEv2] client created imei=356656421305276 imsi=310240184649727 nai=<EMAIL> epdg_address=epdg.epc.mnc240.mcc310.pub.3gppnetwork.org
2025/06/28 19:21:45 INFO Starting VoWiFi connection...
2025/06/28 19:21:45 INFO [SWuClient] initializing SWu connection...
2025/06/28 19:21:45 INFO [Network] resolved ePDG address hostname=epdg.epc.mnc240.mcc310.pub.3gppnetwork.org ip=***********
2025/06/28 19:21:45 INFO [Network] connections established nat_local=************:34781 nat_remote=***********:500 natt_local=************:38164 natt_remote=***********:4500
2025/06/28 19:21:45 INFO [Network] started packet listeners
2025/06/28 19:21:45 INFO [SWuClient] initiating IKE_SA_INIT message_id=0
2025/06/28 19:21:45 DEBUG [SWuClient] created SA proposal encr_id=12 integ_id=1 prf_id=1 dh_id=2
2025/06/28 19:21:45 DEBUG [SWuClient] added SA payload
2025/06/28 19:21:45 DEBUG [SWuClient] added KE payload dh_group=2 key_len=128
2025/06/28 19:21:45 DEBUG [SWuClient] added nonce payload nonce_len=16
2025/06/28 19:21:45 DEBUG [SWuClient] added NAT source payload
2025/06/28 19:21:45 DEBUG [SWuClient] added NAT dest payload
2025/06/28 19:21:45 DEBUG [ResponderManager] registered handler message_id=0 timeout=2s
2025/06/28 19:21:45 DEBUG [Network] sent NAT packet size=288
2025/06/28 19:21:46 DEBUG [SWuClient] received packet from=NAT size=304
2025/06/28 19:21:46 DEBUG [Packet] unmarshaled packet spi_initiator=0xb17073c6bf6e20e4 spi_responder=0x087e046342996404 exchange_type=34 message_id=0 payload_count=5
2025/06/28 19:21:46 INFO [SWuClient] received IKE_SA_INIT response
2025/06/28 19:21:46 DEBUG [SWuClient] parsing IKE_SA_INIT response payloads count=5
2025/06/28 19:21:46 DEBUG [SWuClient] found SA payload index=0 proposals=1
2025/06/28 19:21:46 DEBUG [SWuClient] found KE payload index=1 dh_group=2 length=128
2025/06/28 19:21:46 DEBUG [SWuClient] found nonce payload index=2 length=32
2025/06/28 19:21:46 DEBUG [SWuClient] received NAT detection notification index=3 type=16388 data_len=20
2025/06/28 19:21:46 DEBUG [SWuClient] received NAT detection notification index=4 type=16389 data_len=20
2025/06/28 19:21:46 DEBUG [SWuClient] parsing selected proposal transforms=4
2025/06/28 19:21:46 DEBUG [SWuClient] processing transform index=0 type=1 id=12 attrs=0
2025/06/28 19:21:46 DEBUG [SWuClient] processing transform index=1 type=3 id=1 attrs=0
2025/06/28 19:21:46 DEBUG [SWuClient] processing transform index=2 type=2 id=1 attrs=0
2025/06/28 19:21:46 DEBUG [SWuClient] processing transform index=3 type=4 id=2 attrs=0
2025/06/28 19:21:46 DEBUG [SWuClient] extracted crypto parameters encr=12 encr_length=0 integ=1 prf=1
2025/06/28 19:21:46 DEBUG [SWuClient] defaulting AES-CBC key length to 128
2025/06/28 19:21:46 DEBUG [CryptoHelper] crypto mode set prf=1 integ=1 encr=12 encr_keylen=128
2025/06/28 19:21:46 INFO [Transaction] crypto mode set encr=12 encr_keylen=128 integ=1 prf=1
2025/06/28 19:21:46 DEBUG [CryptoHelper] DH shared secret generated length=128 shared_key_first=735023cb217b4c3d2102dc9b76a3f2b61a6d8c9230d1fc464b531441cd6740d1 private_key_bits=1024 public_key_bits=1024
2025/06/28 19:21:46 DEBUG [CryptoHelper] key derivation inputs dh_key=735023cb217b4c3d2102dc9b76a3f2b61a6d8c9230d1fc464b531441cd6740d17a77f4182c66a5f6c7acb5b34575cafebc65a9633428688990eba3ca799a01168996a5326b36df68227c303a249ef6be1363e105a0e99f147233f32e8b627eba42f59517df2eb2eb26f6e44fd58f1b17128b27a4c4639558a568f8e70be06014 nonce=f03e9a0f3af4822713ea8f232ba2e09dd2aa26c2e0efb8d415f756d093444be88cfabe41b7569c2ab9d5f7b77d8edf3e skeyseed=9b32dbe1c893eabe4e89503abf631593
2025/06/28 19:21:46 DEBUG [CryptoHelper] PRF test key=746573745f6b6579 data=746573745f64617461 result=0d601aa11ed520ee4e5da0ed3636fdce
2025/06/28 19:21:46 DEBUG [CryptoHelper] key derivation seed stream=f03e9a0f3af4822713ea8f232ba2e09dd2aa26c2e0efb8d415f756d093444be88cfabe41b7569c2ab9d5f7b77d8edf3eb17073c6bf6e20e4087e046342996404 spi_initiator=0xb17073c6bf6e20e4 spi_responder=0x087e046342996404
2025/06/28 19:21:46 DEBUG [CryptoHelper] prfPlus first iteration key=9b32dbe1c893eabe4e89503abf631593 seed=f03e9a0f3af4822713ea8f232ba2e09dd2aa26c2e0efb8d415f756d093444be88cfabe41b7569c2ab9d5f7b77d8edf3eb17073c6bf6e20e4087e046342996404 data=f03e9a0f3af4822713ea8f232ba2e09dd2aa26c2e0efb8d415f756d093444be88cfabe41b7569c2ab9d5f7b77d8edf3eb17073c6bf6e20e4087e04634299640401 result=48417125becc1600f7bdb9cb7412e8fb
2025/06/28 19:21:46 INFO [CryptoHelper] keys derived successfully skd_len=16 sk_ai_len=16 sk_ar_len=16 sk_ei_len=16 sk_er_len=16 sk_pi_len=16 sk_pr_len=16
2025/06/28 19:21:46 DEBUG [CryptoHelper] derived keys skeyseed=9b32dbe1c893eabe4e89503abf631593 sk_ai=a9ee675ad391a62b4ad2a831d8273b0f sk_ar=a1b1c9b81626d8727518b5be60b36005 sk_ei=3e328c8c06e6d53db91e62a17cdce987 sk_er=6b5668070d038f359a8eb695dfbf6023
2025/06/28 19:21:46 INFO [SWuClient] IKE_SA_INIT completed, starting IKE_AUTH
2025/06/28 19:21:46 DEBUG [Transaction] incremented message ID message_id=1
2025/06/28 19:21:46 INFO [SWuClient] initiating IKE_AUTH request message_id=1
2025/06/28 19:21:46 DEBUG [ResponderManager] registered handler message_id=1 timeout=2s
2025/06/28 19:21:46 DEBUG [Network] sent NAT-T packet size=94
2025/06/28 19:21:46 DEBUG [ResponderManager] processed response message_id=0
2025/06/28 19:21:46 DEBUG [SWuClient] received packet from=NAT-T size=192
2025/06/28 19:21:46 DEBUG [Packet] unmarshaled packet spi_initiator=0xb17073c6bf6e20e4 spi_responder=0x087e046342996404 exchange_type=35 message_id=1 payload_count=1
2025/06/28 19:21:46 INFO [SWuClient] received IKE_AUTH response
2025/06/28 19:21:46 DEBUG [SWuClient] raw packet data for integrity check len=188 first_bytes=b17073c6bf6e20e4087e0463429964042e20232000000001000000bc240000a0
2025/06/28 19:21:46 DEBUG [SWuClient] packet header info spi_i=b17073c6bf6e20e4 spi_r=87e046342996404 msg_id=1
2025/06/28 19:21:46 DEBUG [SWuClient] IKE message flags flags=0x20 is_response=true is_initiator=false
2025/06/28 19:21:46 DEBUG [SWuClient] parsing IKE_AUTH response payloads count=1
2025/06/28 19:21:46 DEBUG [SWuClient] found encrypted payload index=0 data_len=156
2025/06/28 19:21:46 DEBUG [SWuClient] decrypting encrypted payload
2025/06/28 19:21:46 DEBUG [CryptoHelper] decrypting payload total_len=156 iv_len=16 encrypted_len=128 ic_len=12 iv=b8d9b809064b9402376d1154c5bcf664 encrypted_first=b7b5ef78b0ae1fa76d02835c8f7ca754 ic=a8055880d7a9c58c9e8d8f0d
2025/06/28 19:21:46 DEBUG [CryptoHelper] integrity check details full_packet_len=188 verified_packet_len=176 integ_length=12 received_ic=a8055880d7a9c58c9e8d8f0d calculated_ic_ar=a8055880d7a9c58c9e8d8f0d calculated_ic_ai=dcf24358ade547c45a1149bf sk_ar=a1b1c9b81626d8727518b5be60b36005 sk_ai=a9ee675ad391a62b4ad2a831d8273b0f verified_packet_end=be15d718d63bbb1bef0b76d210c22e14
2025/06/28 19:21:46 INFO [CryptoHelper] integrity check passed!
2025/06/28 19:21:46 DEBUG [CryptoHelper] decryption attempts sk_er_success=true sk_ei_success=true sk_er=6b5668070d038f359a8eb695dfbf6023 sk_ei=3e328c8c06e6d53db91e62a17cdce987 sk_er_len=16 sk_ei_len=16 iv_len=16 iv=b8d9b809064b9402376d1154c5bcf664
2025/06/28 19:21:46 DEBUG [CryptoHelper] SK_er decryption result first_bytes=3000003202000000657064672e657063
2025/06/28 19:21:46 DEBUG [CryptoHelper] SK_ei decryption result first_bytes=c0cf7f660b579b2ef7946b559eb3ed4d
2025/06/28 19:21:46 DEBUG [CryptoHelper] raw decrypted data len=128 first_bytes=3000003202000000657064672e6570632e6d6e633234302e6d63633331302e70
2025/06/28 19:21:46 DEBUG [CryptoHelper] padding info pad_length=1 total_len=128 last_16_bytes=1c9cfa13ffc9979d12bd860100000801
2025/06/28 19:21:46 DEBUG [CryptoHelper] padding validation valid=false
2025/06/28 19:21:46 DEBUG [CryptoHelper] payload decrypted successfully decrypted_len=126
2025/06/28 19:21:46 DEBUG [CryptoHelper] decrypted payload header analysis next_payload=48 flags=0x00 length=50 data_len=126
2025/06/28 19:21:46 DEBUG [SWuClient] parsing decrypted payload chain data_len=126
2025/06/28 19:21:46 DEBUG [SWuClient] decrypted data hex data=3000003202000000657064672e6570632e6d6e633234302e6d63633331302e7075622e336770706e6574776f726b2e6f72670000004c01010048170100000105
2025/06/28 19:21:46 DEBUG [SWuClient] attempting to find next payload type for encrypted payload payload_index=0
2025/06/28 19:21:46 DEBUG [SWuClient] starting payload chain parsing first_payload_type=48
2025/06/28 19:21:46 DEBUG [SWuClient] parsing payload current_type=48 next_type=48 length=50 offset=0 flags=0
2025/06/28 19:21:46 DEBUG [SWuClient] parsing payload current_type=48 next_type=0 length=76 offset=50 flags=0
2025/06/28 19:21:46 DEBUG [SWuClient] payload chain parsing complete payload_count=2
2025/06/28 19:21:46 DEBUG [SWuClient] successfully parsed decrypted payload chain payload_count=2
2025/06/28 19:21:46 DEBUG [SWuClient] found EAP payload in decrypted chain data_len=46
2025/06/28 19:21:46 DEBUG [SWuClient] found EAP payload in decrypted chain data_len=72
2025/06/28 19:21:46 DEBUG [SWuClient] EAP message details code=1 id=1 length=72 data_len=72 raw_data=010100481701000001050000dbc3bf915e53d0c650390e34649d7b3d02050000
2025/06/28 19:21:46 DEBUG [SWuClient] received EAP Request
2025/06/28 19:21:46 DEBUG [SWuClient] parsing EAP-AKA challenge data_len=64 raw_data=01050000dbc3bf915e53d0c650390e34649d7b3d0205000023744403f287000047712404499214630b05000043368d698edd1c9cfa13ffc9979d12bd86010000
2025/06/28 19:21:46 DEBUG [SWuClient] EAP-AKA attribute type=1 length=20 offset=0
[DEBUG_EXTRACT] Go AT_RAND raw_value: 0000dbc3bf915e53d0c650390e34649d7b3d
[DEBUG_EXTRACT] Go AT_RAND extracted: bf915e53d0c650390e34649d7b3d0205
2025/06/28 19:21:46 DEBUG [SWuClient] extracted AT_RAND rand=bf915e53d0c650390e34649d7b3d0205
2025/06/28 19:21:46 DEBUG [SWuClient] EAP-AKA attribute type=2 length=20 offset=20
[DEBUG_EXTRACT] Go AT_AUTN raw_value: 000023744403f28700004771240449921463
[DEBUG_EXTRACT] Go AT_AUTN extracted: 4403f287000047712404499214630b05
2025/06/28 19:21:46 DEBUG [SWuClient] extracted AT_AUTN autn=4403f287000047712404499214630b05
2025/06/28 19:21:46 DEBUG [SWuClient] EAP-AKA attribute type=11 length=20 offset=40
2025/06/28 19:21:46 DEBUG [SWuClient] EAP-AKA attribute type=134 length=4 offset=60
2025/06/28 19:21:46 INFO [Transaction] performing USIM authentication
[DEBUG] USIM Auth - RAND: bf915e53d0c650390e34649d7b3d0205
[DEBUG] USIM Auth - AUTN: 4403f287000047712404499214630b05
2025/06/28 19:21:46 DEBUG [APDU] sending command=00A4040410A0000000871002FFFFFFFF8906190000
[DEBUG] APDU response length: 2, status: 612B
[DEBUG] APDU response data: 612b
[DEBUG] APDU response length: 45, status: 9000
[DEBUG] APDU response data: 6229820278218410a0000000871002ffffffff89061900008a01058b032f0607c6099001408301018301819000
2025/06/28 19:21:46 DEBUG [APDU] received response=6229820278218410A0000000871002FFFFFFFF89061900008A01058B032F0607C609900140830101830181
2025/06/28 19:21:46 DEBUG [APDU] sending command=00A40004026F07
[DEBUG] APDU response length: 2, status: 6119
[DEBUG] APDU response data: 6119
[DEBUG] APDU response length: 27, status: 9000
[DEBUG] APDU response data: 62178202412183026f078a01058b036f0617800200098801389000
2025/06/28 19:21:46 DEBUG [APDU] received response=62178202412183026F078A01058B036F061780020009880138
2025/06/28 19:21:46 DEBUG [APDU] sending command=008800812210BF915E53D0C650390E34649D7B3D0205104403F287000047712404499214630B05
[DEBUG] APDU response length: 2, status: 9862
[DEBUG] APDU response data: 9862
2025/06/28 19:21:46 DEBUG [APDU] received response=""
[DEBUG] APDU response length: 0
[DEBUG] APDU response data: 
2025/06/28 19:21:46 ERROR [Network] NAT-T packet handler error error="response handler error: USIM authentication failed: USIM authentication failed: APDU response data too short: expected 48 bytes, got -2"
