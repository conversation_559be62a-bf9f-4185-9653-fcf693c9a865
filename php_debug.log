[38;5;87m[2025-06-28 11:21:05](IMSClient): [38;5;83m[SUCC] IMSClient master[0m
[38;5;87m[2025-06-28 11:21:05](PCSCUI<PERSON><PERSON>rovider): [38;5;231m[INFO] Found reader: Alcor Link AK9563 00 00[0m
[38;5;87m[2025-06-28 11:21:05](PCSCUICCProvider): [38;5;83m[SUCC] Use reader: Alcor Link AK9563 00 00[0m
[38;5;87m[2025-06-28 11:21:05](Identity): [38;5;231m[INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240[0m
[38;5;87m[2025-06-28 11:21:05](Utils): [0m[DEBUG] ip link del ims8ffdc031[0m
[38;5;87m[2025-06-28 11:21:05](Utils): [0m[DEBUG] ip xfrm policy delete src 2607:fc20:8a9d:a23:ac39:899b:9654:afae/128 dst fd00:976a:d6b7:4::5/128 dir out mark 0xaffdc031[0m
[38;5;87m[2025-06-28 11:21:05](Utils): [0m[DEBUG] ip xfrm policy delete src 2607:fc20:8a9d:a23:ac39:899b:9654:afae/128 dst fd00:976a:d6b7:4::5/128 dir out mark 0x9ffdc031[0m
[38;5;87m[2025-06-28 11:21:05](Utils): [0m[DEBUG] ip xfrm policy delete src ::/0 dst ::/0 dir in mark 0x8ffdc031[0m
[38;5;87m[2025-06-28 11:21:05](Utils): [0m[DEBUG] ip xfrm policy delete src ::/0 dst ::/0 dir out mark 0x8ffdc031[0m
[38;5;87m[2025-06-28 11:21:05](Utils): [0m[DEBUG] ip xfrm state deleteall reqid **********[0m
[38;5;87m[2025-06-28 11:21:05](Utils): [0m[DEBUG] iptables -t nat -D OUTPUT -d ************** -m mark --mark 0x8ffdc031 -j DNAT --to-destination ***********[0m
[38;5;87m[2025-06-28 11:21:05](SWuClient): [38;5;231m[INFO] SWu connection initializing...[0m
[38;5;87m[2025-06-28 11:21:05](Utils): [0m[DEBUG] iptables -t nat -I OUTPUT -m mark --mark ********** -d ************** -j DNAT --to-destination ***********[0m
[38;5;87m[2025-06-28 11:21:05](SWuClient): [38;5;231m[INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************[0m
[38;5;87m[2025-06-28 11:21:05](SWuClient): [38;5;231m[INFO] Initial IKE MSG=00, username: <EMAIL>[0m
[38;5;87m[2025-06-28 11:21:05](SWuClient): [38;5;231m[INFO] Receive IKE MSG=00 Reply[0m
[38;5;87m[2025-06-28 11:21:05](CryptoHelper): [0m[DEBUG] DH_KEY: 6c5d07240960d2c09211ecc31add58bce93e2f03f3e4514ee572fc5cae28966c1cb60c8cc1de1678059e1d7cef75669d58c97e8529da1b74cfa939e68fc0221f836ccd5b0ce736f033d7e75fcb53954b8fa094d2f6cb8de4821276e7e4bc036d85d2cc8742cfc21e430f1be6ed8939a0dad3c9a976bb43dd60e18d2a47c41be2[0m
[38;5;87m[2025-06-28 11:21:05](CryptoHelper): [0m[DEBUG] NONCE: e9b88e9269bbdc080ed4e7f1b54897fffcdd64698c99e0c69f80fb7a9f3f37d22e1f246c667b706eff08e95451345f1b[0m
[38;5;87m[2025-06-28 11:21:05](CryptoHelper): [0m[DEBUG] SKEYSEED: 4b6e23bbc5aac17118bbbe49f75400bd[0m
[38;5;87m[2025-06-28 11:21:05](CryptoHelper): [0m[DEBUG] STREAM: e9b88e9269bbdc080ed4e7f1b54897fffcdd64698c99e0c69f80fb7a9f3f37d22e1f246c667b706eff08e95451345f1b00000000ec40678bb1f16a71430cde03[0m
[38;5;87m[2025-06-28 11:21:05](CryptoHelper): [0m[DEBUG] sk_ d: 3cbe0a6e63fd3f264c42bb1155c246ef[0m
[38;5;87m[2025-06-28 11:21:05](CryptoHelper): [0m[DEBUG] sk_ai: d17a9208335ecbd2174f557bedf6f818[0m
[38;5;87m[2025-06-28 11:21:05](CryptoHelper): [0m[DEBUG] sk_ar: 936661243299c88771a401db34d0776e[0m
[38;5;87m[2025-06-28 11:21:05](CryptoHelper): [0m[DEBUG] sk_ei: dd0a549fda06ed467c1e64ca6dfed67d[0m
[38;5;87m[2025-06-28 11:21:05](CryptoHelper): [0m[DEBUG] sk_er: a8adaf117474ff1e3f09cc48e3062d23[0m
[38;5;87m[2025-06-28 11:21:05](CryptoHelper): [0m[DEBUG] sk_pi: 050fb2898a9f91f5eb07ba6be5d5b88b[0m
[38;5;87m[2025-06-28 11:21:05](CryptoHelper): [0m[DEBUG] sk_pr: 92d8c3f643584725478da182f46a53bc[0m
[38;5;87m[2025-06-28 11:21:05](SWuClient): [0m[DEBUG] Decrypt Table: 00000000ec40678b,b1f16a71430cde03,dd0a549fda06ed467c1e64ca6dfed67d,a8adaf117474ff1e3f09cc48e3062d23,"AES-CBC-128 [RFC3602]",d17a9208335ecbd2174f557bedf6f818,936661243299c88771a401db34d0776e,"HMAC_MD5_96 [RFC2403]"[0m
[38;5;87m[2025-06-28 11:21:05](SWuClient): [38;5;231m[INFO] Initial secure IKE MID=01 EAP-AKA Request[0m
[38;5;87m[2025-06-28 11:21:06](SWuClient): [38;5;231m[INFO] Receive secure IKE MID=01[0m
[DEBUG_EXTRACT] PHP EAP message raw data: 010100481701000001050000ffddf1541bb3929e640d5381df3dce5b02050000454a2382f5ab0000370e14f567b9e7de0b0500001ef99f745b2f61e62181d53ad3c581be86010000
[DEBUG_EXTRACT] PHP AT_RAND raw_value: 0000ffddf1541bb3929e640d5381df3dce5b
[DEBUG_EXTRACT] PHP AT_RAND extracted: ffddf1541bb3929e640d5381df3dce5b
[DEBUG_EXTRACT] PHP AT_AUTN raw_value: 0000454a2382f5ab0000370e14f567b9e7de
[DEBUG_EXTRACT] PHP AT_AUTN extracted: 454a2382f5ab0000370e14f567b9e7de
[DEBUG] PHP USIM Auth - RAND: ffddf1541bb3929e640d5381df3dce5b
[DEBUG] PHP USIM Auth - AUTN: 454a2382f5ab0000370e14f567b9e7de
[38;5;87m[2025-06-28 11:21:06](SWuClient): [38;5;231m[INFO] Initial secure IKE MID=02 EAP-AKA Response[0m
[38;5;87m[2025-06-28 11:21:06](SWuClient): [38;5;231m[INFO] Receive secure IKE MSG=02 EAP-AKA Result[0m
[38;5;87m[2025-06-28 11:21:06](SWuClient): [38;5;83m[SUCC] EAP-AKA Success[0m
[38;5;87m[2025-06-28 11:21:06](SWuClient): [38;5;231m[INFO] Initial secure IKE MID=03 IKE_AUTH Request[0m
[38;5;87m[2025-06-28 11:21:07](SWuClient): [38;5;231m[INFO] Receive secure IKE MSG=03 IKE_AUTH Result[0m
[38;5;87m[2025-06-28 11:21:07](Utils): [0m[DEBUG] ip xfrm state add src ************ dst ************** proto esp spi 43867047 mode tunnel enc "cbc(aes)" "0x9ff599fb89ef52c5bf53a70c79a10b92" auth-trunc "hmac(md5)" "0xa4ccfc71343de15f3414334a2d40d97b" 96 encap espinudp 46379 4500 0.0.0.0 sel src ::/0 dst ::/0 output-mark ********** reqid **********[0m
[38;5;87m[2025-06-28 11:21:07](Utils): [0m[DEBUG] ip xfrm state add src ************** dst ************ proto esp spi 292938264 mode tunnel enc "cbc(aes)" "0x38d66f30f9d250027b8e6d3622902f83" auth-trunc "hmac(md5)" "0xa980dd180dd00a7a5f63d748e582c2d2" 96 encap espinudp 4500 46379 0.0.0.0 sel src ::/0 dst ::/0 reqid **********[0m
[38;5;87m[2025-06-28 11:21:07](Utils): [0m[DEBUG] ip xfrm policy add src ::/0 dst ::/0 dir out tmpl src ************ dst ************** proto esp spi 43867047 mode tunnel reqid ********** mark **********[0m
[38;5;87m[2025-06-28 11:21:07](Utils): [0m[DEBUG] ip xfrm policy add src ::/0 dst ::/0 dir in tmpl src ************** dst ************ proto esp spi 292938264 mode tunnel reqid ********** mark **********[0m
[38;5;87m[2025-06-28 11:21:07](Utils): [0m[DEBUG] ip link add ims8ffdc031 type vti local ************ remote ************** key **********[0m
[38;5;87m[2025-06-28 11:21:07](Utils): [0m[DEBUG] ip addr add 2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4/128 dev ims8ffdc031[0m
[38;5;87m[2025-06-28 11:21:07](Utils): [0m[DEBUG] ip link set ims8ffdc031 mtu 1280 up[0m
[38;5;87m[2025-06-28 11:21:07](Utils): [0m[DEBUG] ip route add fd00:976a:c202:1808::5 src 2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4 dev ims8ffdc031[0m
[38;5;87m[2025-06-28 11:21:07](SWuClient): [38;5;83m[SUCC] SWu connection established[0m
[38;5;87m[2025-06-28 11:21:07](IMSClient): [38;5;231m[INFO] P-CSCF Addr: fd00:976a:c202:1808::5[0m
[38;5;87m[2025-06-28 11:21:07](IMSSocketPool): [38;5;231m[INFO] Binding sockets on: [2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4][0m
[38;5;87m[2025-06-28 11:21:07](IMSSocketPool): [0m[DEBUG] Creating sockets...[0m
[38;5;87m[2025-06-28 11:21:07](IMSSocketPool): [38;5;231m[INFO] Self Port Initial: 19998, Client: 33780, Server: 26779[0m
[38;5;87m[2025-06-28 11:21:07](IMSSocketPool): [38;5;231m[INFO] Starting sockets...[0m
[38;5;87m[2025-06-28 11:21:07](IMSSocketPool): [38;5;83m[SUCC] Initial connected to: [fd00:976a:c202:1808::5]:5060[0m
[38;5;87m[2025-06-28 11:21:07](RegisterSequence): [38;5;231m[INFO] Initial secure negotiation, username: sip:<EMAIL>[0m
[38;5;87m[2025-06-28 11:21:08](RegisterSequence): [38;5;231m[INFO] Received server challenge & IPSec configration[0m
[DEBUG] PHP USIM Auth - RAND: 9e150447375fc927a8ccd5614a7f1ca6
[DEBUG] PHP USIM Auth - AUTN: 01127647252d0000ed52a810c824171b
[38;5;87m[2025-06-28 11:21:08](Identity): [0m[DEBUG] Digest Challenge H(Response): c170814c499357611711b35287a6c70e[0m
[38;5;87m[2025-06-28 11:21:08](Utils): [0m[DEBUG] ip xfrm state add src 2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4 dst fd00:976a:c202:1808::5 proto esp spi 2973256285 mode transport enc "cipher_null" "" auth-trunc "hmac(md5)" "0x6da124203d6e5c05622b7f534faecd68" 96 reqid **********[0m
[38;5;87m[2025-06-28 11:21:08](Utils): [0m[DEBUG] ip xfrm state add src fd00:976a:c202:1808::5 dst 2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4 proto esp spi 4034557993 mode transport enc "cipher_null" "" auth-trunc "hmac(md5)" "0x6da124203d6e5c05622b7f534faecd68" 96 reqid **********[0m
[38;5;87m[2025-06-28 11:21:08](Utils): [0m[DEBUG] ip xfrm policy add src 2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4 dst fd00:976a:c202:1808::5 dir out tmpl src 2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4 dst fd00:976a:c202:1808::5 proto esp spi 2973256285 mode transport reqid ********** mark 2684207153[0m
[38;5;87m[2025-06-28 11:21:08](Utils): [0m[DEBUG] ip xfrm state add src 2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4 dst fd00:976a:c202:1808::5 proto esp spi 2973256284 mode transport enc "cipher_null" "" auth-trunc "hmac(md5)" "0x6da124203d6e5c05622b7f534faecd68" 96 reqid **********[0m
[38;5;87m[2025-06-28 11:21:08](Utils): [0m[DEBUG] ip xfrm state add src fd00:976a:c202:1808::5 dst 2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4 proto esp spi 3669054268 mode transport enc "cipher_null" "" auth-trunc "hmac(md5)" "0x6da124203d6e5c05622b7f534faecd68" 96 reqid **********[0m
[38;5;87m[2025-06-28 11:21:08](Utils): [0m[DEBUG] ip xfrm policy add src 2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4 dst fd00:976a:c202:1808::5 dir out tmpl src 2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4 dst fd00:976a:c202:1808::5 proto esp spi 2973256284 mode transport reqid ********** mark 2952642609[0m
[38;5;87m[2025-06-28 11:21:08](IMSSocketPool): [38;5;83m[SUCC] Server listening on: [2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4]:26779[0m
[38;5;87m[2025-06-28 11:21:08](IMSSocketPool): [0m[DEBUG] Connecting to secured endpoint: [fd00:976a:c202:1808::5]:65529...[0m
[38;5;87m[2025-06-28 11:21:08](IMSSocketPool): [38;5;83m[SUCC] Connected to secured endpoint: [fd00:976a:c202:1808::5]:65529[0m
[38;5;87m[2025-06-28 11:21:08](RegisterSequence): [38;5;231m[INFO] Initial REGISTER...[0m
