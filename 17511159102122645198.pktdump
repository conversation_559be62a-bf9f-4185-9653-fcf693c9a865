O:42:"imsclient\protocol\ims\packet\StatusPacket":2:{s:10:" * headers";a:11:{s:11:"status-line";a:1:{i:0;O:40:"imsclient\protocol\ims\header\StatusLine":3:{s:7:"version";s:7:"SIP/2.0";s:4:"code";i:401;s:6:"reason";s:12:"Unauthorized";}}s:3:"via";a:1:{i:0;O:39:"imsclient\protocol\ims\header\ViaHeader":5:{s:9:" * _value";s:150:"SIP/2.0/TCP [2607:fc20:c1a4:ab37:ac39:900b:64f7:67e1]:41129;received=2607:fc20:c1a4:ab37:ac39:900b:64f7:67e1;rport=41129;branch=z9hG4bKDLm4aa7C15gMzdj";s:8:"protocol";N;s:6:"ipaddr";N;s:4:"port";N;s:6:"branch";N;}}s:4:"path";a:1:{i:0;O:40:"imsclient\protocol\ims\header\PathHeader":2:{s:9:" * _value";s:45:"<sip:[FD00:976A:C301:34::5];transport=tcp;lr>";s:3:"uri";O:33:"imsclient\protocol\ims\uri\SIPUri":5:{s:9:" * _value";s:39:"[FD00:976A:C301:34::5];transport=tcp;lr";s:10:" * _params";a:2:{s:9:"transport";s:3:"tcp";s:2:"lr";b:1;}s:8:"username";N;s:4:"host";s:20:"fd00:976a:c301:34::5";s:4:"port";N;}}}s:0:"";a:3:{i:0;O:43:"imsclient\protocol\ims\header\GenericHeader":1:{s:9:" * _value";s:50:"<sip:[FD00:976A:C301:34::5]:5060;transport=tcp;lr>";}i:1;O:43:"imsclient\protocol\ims\header\GenericHeader":1:{s:9:" * _value";s:44:"SIP;cause=401;text="CC_SIP_UNAUTHORIZED_401"";}i:2;O:43:"imsclient\protocol\ims\header\GenericHeader":1:{s:9:" * _value";s:125:"icid-value=th.pxatf401.sip.t-mobi-1751-115910-155686-1126207578;icid-generated-at=th.pxatf401.sip.t-mobile.com;term-ioi=12345";}}s:2:"to";a:1:{i:0;O:38:"imsclient\protocol\ims\header\ToHeader":3:{s:9:" * _value";s:160:"<sip:<EMAIL>>;tag=h7g4Esbg_mavodi-0-4b-2b-b-ffffffc7-220004-ffffffffffffffff-47660000-681c7359-_2f0dd15-685fe886-64795";s:3:"uri";N;s:3:"tag";N;}}s:4:"from";a:1:{i:0;O:40:"imsclient\protocol\ims\header\FromHeader":3:{s:9:" * _value";s:70:"<sip:<EMAIL>>;tag=PqgQYIt6k5";s:45:" imsclient\protocol\ims\header\FromHeader uri";N;s:45:" imsclient\protocol\ims\header\FromHeader tag";N;}}s:7:"call-id";a:1:{i:0;O:42:"imsclient\protocol\ims\header\CallIDHeader":2:{s:9:" * _value";s:24:"BHUQcIbDUl3v68ImLvXI38Cl";s:5:"value";s:24:"BHUQcIbDUl3v68ImLvXI38Cl";}}s:4:"cseq";a:1:{i:0;O:40:"imsclient\protocol\ims\header\CSeqHeader":3:{s:9:" * _value";s:10:"1 REGISTER";s:3:"seq";s:1:"1";s:6:"method";s:8:"REGISTER";}}s:16:"www-authenticate";a:1:{i:0;O:51:"imsclient\protocol\ims\header\WWWAuthenticateHeader":7:{s:9:" * _value";s:121:"Digest realm="ims.mnc240.mcc310.3gppnetwork.org",nonce="oKIwY2UVVXx8iu8tbKrGU9q7ibFMEQAANLmU1rtqpGI=",algorithm=AKAv1-MD5";s:3:"uri";N;s:7:"useruri";N;s:5:"nonce";s:44:"oKIwY2UVVXx8iu8tbKrGU9q7ibFMEQAANLmU1rtqpGI=";s:8:"response";N;s:5:"realm";s:33:"ims.mnc240.mcc310.3gppnetwork.org";s:9:"algorithm";s:9:"AKAv1-MD5";}}s:15:"security-server";a:1:{i:0;O:50:"imsclient\protocol\ims\header\SecurityServerHeader":2:{s:9:" * _value";s:121:"ipsec-3gpp;q=0.5;alg=hmac-md5-96;prot=esp;mod=trans;ealg=null;spi-c=4140708782;spi-s=4140708783;port-c=65528;port-s=65529";s:6:"params";a:1:{i:0;O:54:"imsclient\protocol\ims\header\BasicSecurityHeaderParam":10:{s:9:"mechanism";s:10:"ipsec-3gpp";s:3:"alg";s:11:"hmac-md5-96";s:4:"ealg";s:4:"null";s:3:"mod";s:5:"trans";s:6:"port_c";s:5:"65528";s:6:"port_s";s:5:"65529";s:4:"prot";s:3:"esp";s:5:"spi_c";s:10:"4140708782";s:5:"spi_s";s:10:"4140708783";s:1:"q";s:3:"0.5";}}}}s:14:"content-length";a:1:{i:0;O:49:"imsclient\protocol\ims\header\ContentLengthHeader":2:{s:9:" * _value";s:1:"0";s:6:"length";i:0;}}}s:7:" * body";N;}