package usim

import (
	"errors"
	"fmt"

	"github.com/damonto/vowifi/apdu"
)

func (u *USIM) Auth(rand, autn []byte) (result []byte, ik []byte, ck []byte, err error) {
	if len(rand) != 16 || len(autn) != 16 {
		return nil, nil, nil, errors.New("the length of rand and autn must be 32 hex characters (16 bytes)")
	}

	// Debug: print RAND and AUTN values
	fmt.Printf("[DEBUG] USIM Auth - RAND: %x\n", rand)
	fmt.Printf("[DEBUG] USIM Auth - AUTN: %x\n", autn)

	// Tag 0x10 + RAND (16 bytes) + Tag 0x10 + AUTN (16 bytes) - as per PHP implementation
	data := append([]byte{}, 0x10) // RAND tag
	data = append(data, rand...)
	data = append(data, 0x10) // AUTN tag
	data = append(data, autn...)

	request := apdu.Request{
		CLA: 0x00, INS: 0x88,
		P1: 0x00, P2: 0x81, // 3G context
		Data: data,
	}
	response, err := u.RunAPDU(request)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("APDU command failed: %w", err)
	}

	// Debug: print response details
	fmt.Printf("[DEBUG] APDU response length: %d\n", len(response))
	fmt.Printf("[DEBUG] APDU response data: %x\n", response)

	// Check status code
	if len(response) >= 2 {
		sw1 := response[len(response)-2]
		sw2 := response[len(response)-1]
		sw := uint16(sw1)<<8 | uint16(sw2)
		fmt.Printf("[DEBUG] APDU status code: %04X (SW1=%02X, SW2=%02X)\n", sw, sw1, sw2)

		if sw != 0x9000 {
			return nil, nil, nil, fmt.Errorf("USIM authentication failed with status %04X", sw)
		}
	}

	// Remove status bytes from response data
	dataLen := len(response) - 2
	if dataLen < 48 {
		return nil, nil, nil, fmt.Errorf("APDU response data too short: expected 48 bytes, got %d", dataLen)
	}

	responseData := response[:dataLen]
	return responseData[0:16], responseData[16:32], responseData[32:48], nil
}
