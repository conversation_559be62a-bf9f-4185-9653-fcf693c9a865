package usim

import (
	"errors"
	"fmt"

	"github.com/damonto/vowifi/apdu"
)

func (u *USIM) Auth(rand, autn []byte) (result []byte, ik []byte, ck []byte, err error) {
	if len(rand) != 16 || len(autn) != 16 {
		return nil, nil, nil, errors.New("the length of rand and autn must be 32 hex characters (16 bytes)")
	}
	// Tag 0x10 + Length 0x10 + RAND (16 bytes) + Tag 0x10 + Length 0x10 + AUTN (16 bytes)
	data := append([]byte{}, 0x10, 0x10) // RAND tag + length
	data = append(data, rand...)
	data = append(data, 0x10, 0x10) // AUTN tag + length
	data = append(data, autn...)

	request := apdu.Request{
		CLA: 0x00, INS: 0x88,
		P1: 0x00, P2: 0x81, // 3G context
		Data: data,
	}
	response, err := u.RunAPDU(request)
	if err != nil {
		return nil, nil, nil, fmt.Erro<PERSON>("APDU command failed: %w", err)
	}
	fmt.Println("APDU response:", response)
	return response[0:16], response[16:32], response[32:48], nil
}
