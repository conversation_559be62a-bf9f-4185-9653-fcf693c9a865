package usim

import (
	"errors"
	"fmt"

	"github.com/damonto/vowifi/apdu"
)

func (u *USIM) Auth(rand, autn []byte) (result []byte, ik []byte, ck []byte, err error) {
	if len(rand) != 16 || len(autn) != 16 {
		return nil, nil, nil, errors.New("the length of rand and autn must be 32 hex characters (16 bytes)")
	}

	// Debug: print RAND and AUTN values
	fmt.Printf("[DEBUG] USIM Auth - RAND: %x\n", rand)
	fmt.Printf("[DEBUG] USIM Auth - AUTN: %x\n", autn)

	// Re-select USIM application before authentication
	if _, err := u.Select(SelectName, u.AID); err != nil {
		return nil, nil, nil, fmt.Errorf("failed to select USIM application: %w", err)
	}

	// Try to read a file to ensure USIM is properly selected and accessible
	// This helps ensure the USIM context is properly established
	_, err = u.Select(SelectID, EF_IMSI)
	if err != nil {
		// Log the error but don't fail - this is just a verification step
		fmt.Printf("[DEBUG] USIM verification warning: %v\n", err)
	}

	// Tag 0x10 + RAND (16 bytes) + Tag 0x10 + AUTN (16 bytes) - as per PHP implementation
	data := append([]byte{}, 0x10) // RAND tag
	data = append(data, rand...)
	data = append(data, 0x10) // AUTN tag
	data = append(data, autn...)

	request := apdu.Request{
		CLA: 0x00, INS: 0x88,
		P1: 0x00, P2: 0x81, // 3G context
		Data: data,
	}
	response, err := u.transmitter.Transmit(request.APDU())
	if err != nil {
		return nil, nil, nil, fmt.Errorf("APDU command failed: %w", err)
	}

	// Debug: print response details
	fmt.Printf("[DEBUG] APDU response length: %d\n", len(response))
	fmt.Printf("[DEBUG] APDU response data: %x\n", response)

	// Driver layer already handled status code validation
	// If we get here, the status code was 9000 (success)
	fmt.Printf("[DEBUG] APDU status code: 9000 (success)\n")

	// Response data (status code already removed by driver layer)
	dataLen := len(response)
	if dataLen < 40 {
		return nil, nil, nil, fmt.Errorf("APDU response data too short: expected at least 40 bytes, got %d", dataLen)
	}

	responseData := response
	fmt.Printf("[DEBUG] APDU response data analysis: total_len=%d, data=%x\n", dataLen, responseData)

	// Parse the response according to 3GPP TS 31.102
	// The response format is: DB || RES_len || RES || CK || IK || [additional data]
	if responseData[0] != 0xDB {
		return nil, nil, nil, fmt.Errorf("invalid response tag: expected 0xDB, got 0x%02X", responseData[0])
	}

	resLen := int(responseData[1])
	if resLen < 4 || resLen > 16 {
		return nil, nil, nil, fmt.Errorf("invalid RES length: %d", resLen)
	}

	if dataLen < 2+resLen+32 {
		return nil, nil, nil, fmt.Errorf("response too short for RES(%d)+CK(16)+IK(16): got %d", resLen, dataLen)
	}

	res := responseData[2 : 2+resLen]
	ck = responseData[2+resLen : 2+resLen+16]
	ik = responseData[2+resLen+16 : 2+resLen+32]

	fmt.Printf("[DEBUG] USIM Auth Success - RES(%d): %x, CK: %x, IK: %x\n", resLen, res, ck, ik)

	return res, ik, ck, nil
}
