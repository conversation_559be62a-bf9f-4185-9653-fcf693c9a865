# VoWiFi Client 使用说明

## 项目概述

这是一个完整的VoWiFi (Voice over WiFi) 客户端实现，使用Go语言开发，支持SWu IKEv2认证和隧道建立。

## 功能特性

- ✅ **完整的SWu IKEv2协议实现**：支持完整的4阶段认证流程
- ✅ **USIM卡集成**：自动从USIM卡获取认证参数
- ✅ **ePDG连接**：自动构建ePDG地址并建立连接
- ✅ **EAP-AKA认证**：使用USIM凭据进行3GPP认证
- ✅ **IPSec隧道**：ESP隧道模式，支持加密和完整性保护
- ✅ **现代Go实现**：使用最新Go特性和最佳实践
- ✅ **结构化日志**：使用slog进行调试和监控

## 编译项目

```bash
# 编译项目
go build -o vowifi .

# 检查编译结果
ls -la vowifi
```

## 使用方法

### 1. 准备工作

1. **连接USIM卡读卡器**
2. **更新读卡器名称**（如果需要）：
   ```go
   // 在main.go第31行修改
   d.SetReader("Alcor Link AK9563 00 00")  // 改为你的读卡器名称
   ```
3. **更新设备IMEI**：
   ```go
   // 在main.go第68行修改
   imei, err := usim.NewIMEI("3566564213052700")  // 替换为你的设备IMEI
   ```

### 2. 运行客户端

```bash
# 需要root权限运行（访问网络和智能卡）
sudo ./vowifi
```

### 3. 预期输出

```
INFO USIM Information imsi=123456789012345 mcc=123 mnc=456 iccid=89860000000000000000
INFO [Swu IKEv2] using default ePDG address address=epdg.epc.mnc456.mcc123.pub.3gppnetwork.org
INFO [SWuClient] created imei=3566564213052700 imsi=123456789012345 nai=<EMAIL> epdg_address=epdg.epc.mnc456.mcc123.pub.3gppnetwork.org
INFO Starting VoWiFi connection...
INFO [SWuClient] initializing SWu connection...
DEBUG [CryptoHelper] DH key pair generated group=2
INFO VoWiFi connection established successfully client_address=********00 pcscf_address=********
INFO VoWiFi tunnel is active. Press Ctrl+C to disconnect.
```

## 协议流程

### 阶段1：IKE_SA_INIT交换
- 客户端发送：SA, KE, Nonce, NAT-D载荷
- 服务器响应：SA, KE, Nonce, NAT-D载荷
- 计算DH共享密钥，派生IKE密钥

### 阶段2：IKE_AUTH交换（EAP-AKA）
- 客户端发送：ID载荷（包含NAI）
- 服务器响应：EAP-AKA挑战（RAND, AUTN）
- 客户端执行USIM认证
- 客户端发送：EAP-AKA响应（RES）
- 服务器发送：EAP成功

### 阶段3：最终IKE_AUTH交换
- 客户端发送：AUTH载荷
- 服务器响应：AUTH, SA, CP载荷
- 配置包括：客户端IP, P-CSCF IP, DNS服务器

### 阶段4：IPSec隧道建立
- ESP隧道模式，支持加密和完整性保护
- 客户端现在可以与IMS网络通信

## 故障排除

### 常见问题

1. **读卡器未找到**
   ```
   ERROR Failed to create CCID driver error="..."
   ```
   - 检查读卡器连接
   - 更新main.go中的读卡器名称
   - 验证CCID驱动支持

2. **USIM认证失败**
   ```
   ERROR Failed to initialize USIM error="..."
   ```
   - 确保USIM卡正确插入
   - 检查USIM卡兼容性
   - 验证网络运营商支持

3. **ePDG连接失败**
   ```
   ERROR VoWiFi connection failed error="failed to dial ePDG: ..."
   ```
   - 检查网络连接
   - 验证ePDG地址构建
   - 检查防火墙设置（UDP端口500, 4500）

4. **IKEv2认证失败**
   ```
   ERROR VoWiFi connection failed error="IKE_AUTH failed: ..."
   ```
   - 验证USIM认证参数
   - 检查IMEI有效性
   - 确保NAI格式正确

## 调试模式

客户端默认运行在DEBUG级别，显示详细信息：
- IKEv2数据包详情
- 加密操作
- 网络事件
- USIM交互

## 测试

运行测试套件：

```bash
# 运行所有测试
go test ./...

# 运行SWu模块测试
go test ./swu -v

# 运行示例
go test ./swu -run Example -v
```

## 项目结构

```
├── main.go              # 主应用程序入口点
├── swu/                 # SWu IKEv2实现
│   ├── client.go        # 主SWu客户端
│   ├── handlers.go      # IKEv2响应处理器
│   ├── crypto.go        # 加密操作
│   ├── packet.go        # IKEv2数据包处理
│   ├── payload.go       # IKEv2载荷类型
│   ├── network.go       # 网络层
│   ├── transaction.go   # 状态管理
│   └── ikev2.go         # 高级IKEv2接口
├── usim/                # USIM卡接口
├── driver/              # 读卡器驱动
└── apdu/                # APDU命令处理
```

## 配置说明

客户端自动执行以下配置：
- 从USIM卡获取IMSI, MCC, MNC
- 构建ePDG地址：`epdg.epc.mnc{MNC}.mcc{MCC}.pub.3gppnetwork.org`
- 生成NAI：`0{IMSI}@nai.epc.mnc{MNC}.mcc{MCC}.3gppnetwork.org`
- 使用USIM参数执行3GPP认证

## 安全注意事项

- 需要root权限访问网络和智能卡
- USIM认证参数安全处理
- 加密隧道保护通信
- 支持NAT穿越（NAT-T）

## 参考文档

- 3GPP TS 24.302: 通过非3GPP接入网络访问3GPP演进分组核心网(EPC)
- RFC 7296: 互联网密钥交换协议版本2 (IKEv2)
- RFC 4187: 第三代认证和密钥协商的可扩展认证协议方法 (EAP-AKA)
