package apdu

import (
	"bytes"
	"fmt"
	"io"
	"sync"
)

type Transmitter struct {
	mutex    sync.Mutex
	usim     USIM
	response *bytes.Buffer
}

func NewTransmitter(usim USIM) (io.ReadWriteCloser, error) {
	if err := usim.Connect(); err != nil {
		return nil, err
	}
	return &Transmitter{usim: usim}, nil
}

func (t *Transmitter) Read(p []byte) (n int, err error) {
	return t.response.Read(p)
}

func (t *Transmitter) Write(command []byte) (n int, err error) {
	t.response = new(bytes.Buffer)
	var response Response
	if response, err = t.transmit(command); err != nil {
		return 0, err
	}
	n += len(response.Data())
	if !response.HasMore() {
		t.response.Write(response.Data())
		return n, nil
	}
	if response.HasMore() {
		if err := t.readCommandResponse(t.response, response.SW2()); err != nil {
			return 0, err
		}
	}
	return n, nil
}

func (t *Transmitter) transmit(command []byte) (response Response, err error) {
	t.mutex.Lock()
	response, err = t.usim.Transmit(command)
	t.mutex.Unlock()
	if err != nil {
		return nil, err
	}
	// Debug: print response details
	if response != nil {
		fmt.Printf("[DEBUG] APDU response length: %d, status: %04X\n", len(response), response.SW())
		if len(response) > 0 {
			fmt.Printf("[DEBUG] APDU response data: %x\n", []byte(response))
		}
	}

	if !response.OK() && !response.HasMore() {
		// Allow certain status codes to pass through for application-level handling
		sw := response.SW()
		if sw == 0x9862 || sw == 0x9840 || sw == 0x6982 || sw == 0x6985 {
			// Authentication-related status codes - let application handle them
			return response, nil
		}
		return nil, fmt.Errorf("returned an unexpected response with status %04X", response.SW())
	}
	return response, nil
}

func (t *Transmitter) readCommandResponse(w io.Writer, le byte) error {
	var err error
	var request Request
	var response Response
	request.CLA = 0x00
	request.INS = 0xC0
	request.Le = &le
	for {
		if response, err = t.transmit(request.APDU()); err != nil {
			return err
		}
		if _, err = w.Write(response.Data()); err != nil {
			return err
		}
		if !response.HasMore() {
			break
		}
		*request.Le = response.SW2()
	}
	return nil
}

func (t *Transmitter) Close() error {
	return t.usim.Close()
}
