package swu

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/md5"
	"fmt"
	"log/slog"
	"net"
	"sync"
)

// UserSpaceIPSec implements a simplified userspace IPSec for VoWiFi
type UserSpaceIPSec struct {
	logger *slog.Logger
	
	// Tunnel configuration
	localIP    net.IP
	remoteIP   net.IP
	pcscfIP    net.IP
	
	// ESP parameters
	spiInitiator uint32
	spiResponder uint32
	
	// Encryption keys
	encrKeyInitiator []byte
	encrKeyResponder []byte
	authKeyInitiator []byte
	authKeyResponder []byte
	
	// Cipher instances
	encrCipherInit cipher.Block
	encrCipherResp cipher.Block
	
	// Connection state
	established bool
	mutex       sync.RWMutex
}

// NewUserSpaceIPSec creates a new userspace IPSec implementation
func NewUserSpaceIPSec(logger *slog.Logger) *UserSpaceIPSec {
	return &UserSpaceIPSec{
		logger: logger,
	}
}

// Configure sets up the IPSec tunnel parameters
func (ipsec *UserSpaceIPSec) Configure(localIP, remoteIP, pcscfIP string, 
	spiInit, spiResp uint32, 
	encrKeyInit, encrKeyResp, authKeyInit, authKeyResp []byte) error {
	
	ipsec.mutex.Lock()
	defer ipsec.mutex.Unlock()
	
	ipsec.logger.Info("[UserSpaceIPSec] configuring tunnel",
		"local_ip", localIP,
		"remote_ip", remoteIP,
		"pcscf_ip", pcscfIP,
		"spi_init", fmt.Sprintf("0x%x", spiInit),
		"spi_resp", fmt.Sprintf("0x%x", spiResp))
	
	// Parse IP addresses
	ipsec.localIP = net.ParseIP(localIP)
	ipsec.remoteIP = net.ParseIP(remoteIP)
	ipsec.pcscfIP = net.ParseIP(pcscfIP)
	
	if ipsec.localIP == nil || ipsec.remoteIP == nil {
		return fmt.Errorf("invalid IP addresses")
	}
	
	// Store ESP parameters
	ipsec.spiInitiator = spiInit
	ipsec.spiResponder = spiResp
	
	// Store keys
	ipsec.encrKeyInitiator = make([]byte, len(encrKeyInit))
	copy(ipsec.encrKeyInitiator, encrKeyInit)
	
	ipsec.encrKeyResponder = make([]byte, len(encrKeyResp))
	copy(ipsec.encrKeyResponder, encrKeyResp)
	
	ipsec.authKeyInitiator = make([]byte, len(authKeyInit))
	copy(ipsec.authKeyInitiator, authKeyInit)
	
	ipsec.authKeyResponder = make([]byte, len(authKeyResp))
	copy(ipsec.authKeyResponder, authKeyResp)
	
	// Create cipher instances
	var err error
	ipsec.encrCipherInit, err = aes.NewCipher(ipsec.encrKeyInitiator)
	if err != nil {
		return fmt.Errorf("failed to create initiator cipher: %w", err)
	}
	
	ipsec.encrCipherResp, err = aes.NewCipher(ipsec.encrKeyResponder)
	if err != nil {
		return fmt.Errorf("failed to create responder cipher: %w", err)
	}
	
	ipsec.logger.Info("[UserSpaceIPSec] tunnel configured successfully")
	return nil
}

// Establish marks the tunnel as established
func (ipsec *UserSpaceIPSec) Establish() error {
	ipsec.mutex.Lock()
	defer ipsec.mutex.Unlock()
	
	ipsec.established = true
	ipsec.logger.Info("[UserSpaceIPSec] tunnel established")
	
	return nil
}

// IsEstablished returns whether the tunnel is established
func (ipsec *UserSpaceIPSec) IsEstablished() bool {
	ipsec.mutex.RLock()
	defer ipsec.mutex.RUnlock()
	return ipsec.established
}

// EncryptPacket encrypts an outgoing packet using ESP
func (ipsec *UserSpaceIPSec) EncryptPacket(plaintext []byte) ([]byte, error) {
	ipsec.mutex.RLock()
	defer ipsec.mutex.RUnlock()
	
	if !ipsec.established {
		return nil, fmt.Errorf("tunnel not established")
	}
	
	// Simplified ESP packet construction
	// In a real implementation, this would include proper ESP headers, padding, etc.
	
	// For now, just return the plaintext with a simple header
	espPacket := make([]byte, 8+len(plaintext))
	
	// ESP header (simplified)
	espPacket[0] = byte(ipsec.spiInitiator >> 24)
	espPacket[1] = byte(ipsec.spiInitiator >> 16)
	espPacket[2] = byte(ipsec.spiInitiator >> 8)
	espPacket[3] = byte(ipsec.spiInitiator)
	
	// Sequence number (simplified)
	espPacket[4] = 0
	espPacket[5] = 0
	espPacket[6] = 0
	espPacket[7] = 1
	
	// Copy payload
	copy(espPacket[8:], plaintext)
	
	ipsec.logger.Debug("[UserSpaceIPSec] encrypted packet", "size", len(espPacket))
	return espPacket, nil
}

// DecryptPacket decrypts an incoming ESP packet
func (ipsec *UserSpaceIPSec) DecryptPacket(ciphertext []byte) ([]byte, error) {
	ipsec.mutex.RLock()
	defer ipsec.mutex.RUnlock()
	
	if !ipsec.established {
		return nil, fmt.Errorf("tunnel not established")
	}
	
	if len(ciphertext) < 8 {
		return nil, fmt.Errorf("packet too short")
	}
	
	// Extract SPI
	spi := uint32(ciphertext[0])<<24 | uint32(ciphertext[1])<<16 | 
		  uint32(ciphertext[2])<<8 | uint32(ciphertext[3])
	
	// Verify SPI
	if spi != ipsec.spiResponder {
		return nil, fmt.Errorf("invalid SPI: expected 0x%x, got 0x%x", ipsec.spiResponder, spi)
	}
	
	// Extract payload (simplified)
	payload := make([]byte, len(ciphertext)-8)
	copy(payload, ciphertext[8:])
	
	ipsec.logger.Debug("[UserSpaceIPSec] decrypted packet", "size", len(payload))
	return payload, nil
}

// CalculateHMAC calculates HMAC for authentication
func (ipsec *UserSpaceIPSec) CalculateHMAC(data []byte, isInitiator bool) []byte {
	var key []byte
	if isInitiator {
		key = ipsec.authKeyInitiator
	} else {
		key = ipsec.authKeyResponder
	}
	
	h := hmac.New(md5.New, key)
	h.Write(data)
	return h.Sum(nil)[:12] // Truncate to 96 bits for HMAC-MD5-96
}

// GetLocalIP returns the local IP address
func (ipsec *UserSpaceIPSec) GetLocalIP() net.IP {
	ipsec.mutex.RLock()
	defer ipsec.mutex.RUnlock()
	return ipsec.localIP
}

// GetRemoteIP returns the remote IP address
func (ipsec *UserSpaceIPSec) GetRemoteIP() net.IP {
	ipsec.mutex.RLock()
	defer ipsec.mutex.RUnlock()
	return ipsec.remoteIP
}

// GetPCSCFIP returns the P-CSCF IP address
func (ipsec *UserSpaceIPSec) GetPCSCFIP() net.IP {
	ipsec.mutex.RLock()
	defer ipsec.mutex.RUnlock()
	return ipsec.pcscfIP
}

// Close closes the IPSec tunnel
func (ipsec *UserSpaceIPSec) Close() error {
	ipsec.mutex.Lock()
	defer ipsec.mutex.Unlock()
	
	ipsec.established = false
	ipsec.logger.Info("[UserSpaceIPSec] tunnel closed")
	
	return nil
}

// GetTunnelInfo returns tunnel information for debugging
func (ipsec *UserSpaceIPSec) GetTunnelInfo() map[string]interface{} {
	ipsec.mutex.RLock()
	defer ipsec.mutex.RUnlock()
	
	return map[string]interface{}{
		"established":     ipsec.established,
		"local_ip":        ipsec.localIP.String(),
		"remote_ip":       ipsec.remoteIP.String(),
		"pcscf_ip":        ipsec.pcscfIP.String(),
		"spi_initiator":   fmt.Sprintf("0x%x", ipsec.spiInitiator),
		"spi_responder":   fmt.Sprintf("0x%x", ipsec.spiResponder),
		"encr_key_len":    len(ipsec.encrKeyInitiator),
		"auth_key_len":    len(ipsec.authKeyInitiator),
	}
}
