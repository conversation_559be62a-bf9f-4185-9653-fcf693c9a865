package swu

import (
	"fmt"
	"log/slog"
	"os"
)

// ExampleSWuClient demonstrates how to use the SWu client
func ExampleSWuClient() {
	// This example shows the API usage but won't actually run
	// since it requires a real USIM card and ePDG connection
	fmt.Println("SWu Client Example")
	fmt.Println("==================")
	fmt.Println()
	fmt.Println("1. Create USIM instance (requires real USIM card)")
	fmt.Println("   usimCard, err := usim.NewUSIM(reader)")
	fmt.Println()
	fmt.Println("2. Create SWu client")
	fmt.Println("   ctx := context.Background()")
	fmt.Println("   client, err := NewSWuClient(ctx, imei, usimCard, epdgAddress, mark, logger)")
	fmt.Println()
	fmt.Println("3. Run SWu authentication")
	fmt.Println("   err = client.Run()")
	fmt.Println()
	fmt.Println("4. Get assigned addresses")
	fmt.Println("   clientAddr := client.GetClientAddress()")
	fmt.Println("   pcscfAddr := client.GetPCSCFAddress()")
	fmt.Println()
	fmt.Println("5. Use IKEv2 wrapper")
	fmt.Println("   ikev2, err := NewIKEv2(ctx, usimCard, imei, epdgAddress, logger)")
	fmt.Println("   err = ikev2.Listen()")
	fmt.Println("   clientAddr := ikev2.GetClientAddress()")
	fmt.Println("   pcscfAddr := ikev2.GetPCSCFAddress()")

	// Note: Actual implementation requires a real USIM card and ePDG connection

	// Output:
	// SWu Client Example
	// ==================
	//
	// 1. Create USIM instance (requires real USIM card)
	//    usimCard, err := usim.NewUSIM(reader)
	//
	// 2. Create SWu client
	//    ctx := context.Background()
	//    client, err := NewSWuClient(ctx, imei, usimCard, epdgAddress, mark, logger)
	//
	// 3. Run SWu authentication
	//    err = client.Run()
	//
	// 4. Get assigned addresses
	//    clientAddr := client.GetClientAddress()
	//    pcscfAddr := client.GetPCSCFAddress()
	//
	// 5. Use IKEv2 wrapper
	//    ikev2, err := NewIKEv2(ctx, usimCard, imei, epdgAddress, logger)
	//    err = ikev2.Listen()
	//    clientAddr := ikev2.GetClientAddress()
	//    pcscfAddr := ikev2.GetPCSCFAddress()
}

// ExampleSWuClient_workflow demonstrates the complete IKEv2 workflow
func ExampleSWuClient_workflow() {
	fmt.Println("IKEv2 SWu Protocol Flow")
	fmt.Println("=======================")
	fmt.Println()
	fmt.Println("1. IKE_SA_INIT Exchange:")
	fmt.Println("   - Client sends SA, KE, Nonce, NAT-D payloads")
	fmt.Println("   - Server responds with SA, KE, Nonce, NAT-D payloads")
	fmt.Println("   - DH shared secret is computed")
	fmt.Println("   - IKE keys are derived")
	fmt.Println()
	fmt.Println("2. IKE_AUTH Exchange (EAP-AKA):")
	fmt.Println("   - Client sends ID payload with NAI")
	fmt.Println("   - Server responds with EAP-AKA Challenge (RAND, AUTN)")
	fmt.Println("   - Client performs USIM authentication")
	fmt.Println("   - Client sends EAP-AKA Response (RES)")
	fmt.Println("   - Server sends EAP Success")
	fmt.Println()
	fmt.Println("3. Final IKE_AUTH Exchange:")
	fmt.Println("   - Client sends AUTH payload")
	fmt.Println("   - Server responds with AUTH, SA, CP payloads")
	fmt.Println("   - Configuration payload contains:")
	fmt.Println("     * Client IP address (IPv4/IPv6)")
	fmt.Println("     * P-CSCF IP address")
	fmt.Println("     * DNS servers")
	fmt.Println()
	fmt.Println("4. IPSec Tunnel Established:")
	fmt.Println("   - ESP tunnel mode with encryption and integrity")
	fmt.Println("   - Client can now communicate with IMS network")

	// Output:
	// IKEv2 SWu Protocol Flow
	// =======================
	//
	// 1. IKE_SA_INIT Exchange:
	//    - Client sends SA, KE, Nonce, NAT-D payloads
	//    - Server responds with SA, KE, Nonce, NAT-D payloads
	//    - DH shared secret is computed
	//    - IKE keys are derived
	//
	// 2. IKE_AUTH Exchange (EAP-AKA):
	//    - Client sends ID payload with NAI
	//    - Server responds with EAP-AKA Challenge (RAND, AUTN)
	//    - Client performs USIM authentication
	//    - Client sends EAP-AKA Response (RES)
	//    - Server sends EAP Success
	//
	// 3. Final IKE_AUTH Exchange:
	//    - Client sends AUTH payload
	//    - Server responds with AUTH, SA, CP payloads
	//    - Configuration payload contains:
	//      * Client IP address (IPv4/IPv6)
	//      * P-CSCF IP address
	//      * DNS servers
	//
	// 4. IPSec Tunnel Established:
	//    - ESP tunnel mode with encryption and integrity
	//    - Client can now communicate with IMS network
}

// ExampleGenericPacket demonstrates IKEv2 packet structure
func ExampleGenericPacket() {
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError, // Only show errors to avoid log output in example
	}))

	// Create a sample transaction
	transaction, _ := NewTransaction("123456789012345", "<EMAIL>", logger)

	// Create IKE_SA_INIT packet
	packet := NewGenericPacket(transaction, ExchangeTypeIKESAInit)

	// Add Security Association payload
	saPayload := &SecurityAssociationPayload{
		Proposals: []Proposal{
			{
				ProposalNumber: 1,
				ProtocolID:     1, // IKE
				SPISize:        0,
				Transforms: []Transform{
					{TransformType: TransformTypeEncr, TransformID: EncrAESCBC},
					{TransformType: TransformTypeInteg, TransformID: AuthHMACSHA196},
					{TransformType: TransformTypePRF, TransformID: PRFHMACSHA1},
					{TransformType: TransformTypeDH, TransformID: DHGroup1024MODP},
				},
			},
		},
	}
	packet.AddPayload(saPayload)

	// Add Key Exchange payload
	crypto := NewCryptoHelper(logger)
	crypto.SetCryptoDH(DHGroup1024MODP)
	kePayload := &KeyExchangePayload{
		DHGroup: DHGroup1024MODP,
		Data:    crypto.GetDHPublicKey(),
	}
	packet.AddPayload(kePayload)

	// Add Nonce payload
	noncePayload := &NoncePayload{Data: transaction.NonceInitiator}
	packet.AddPayload(noncePayload)

	// Marshal packet
	data, err := packet.Marshal()
	if err != nil {
		logger.Error("Failed to marshal packet", "error", err)
		return
	}

	fmt.Printf("IKE_SA_INIT packet structure:\n")
	fmt.Printf("- Total size: %d bytes\n", len(data))
	fmt.Printf("- IKE header: 28 bytes\n")
	fmt.Printf("- Payload data: %d bytes\n", len(data)-28)
	fmt.Printf("- Number of payloads: %d\n", len(packet.Payloads))
	fmt.Printf("- Exchange type: %d (IKE_SA_INIT)\n", packet.ExchangeType)
	fmt.Printf("- Message ID: %d\n", packet.MessageID)

	// Output:
	// IKE_SA_INIT packet structure:
	// - Total size: 228 bytes
	// - IKE header: 28 bytes
	// - Payload data: 200 bytes
	// - Number of payloads: 3
	// - Exchange type: 34 (IKE_SA_INIT)
	// - Message ID: 0
}
