package swu

import (
	"context"
	"fmt"
	"log/slog"

	"github.com/damonto/vowifi/usim"
)

type IKEv2 struct {
	USIM        *usim.USIM
	IMEI        usim.IMEI
	ePDGAddress string
	NAI         string
	ctx         context.Context

	logger  *slog.Logger
	network *Network
}

func NewIKEv2(ctx context.Context, usim *usim.USIM, imei usim.IMEI, address string, logger *slog.Logger) (*IKEv2, error) {
	i := IKEv2{USIM: usim, IMEI: imei, logger: logger, ePDGAddress: address, ctx: ctx}
	imsi, err := usim.GetIMSI()
	if err != nil {
		return nil, fmt.Errorf("failed to get IMSI: %w", err)
	}
	if i.ePDGAddress == "" {
		i.ePDGAddress = fmt.Sprintf("epdg.epc.mnc%s.mcc%s.pub.3gppnetwork.org", imsi.MNC(), imsi.MCC())
		i.logger.Info("[Swu IKEv2] using default ePDG address", "address", i.ePDGAddress)
	} else {
		i.logger.Info("[Swu IKEv2] using provided ePDG address", "address", i.ePDGAddress)
	}
	if i.network, err = NewNetwork(ctx, i.ePDGAddress, i.logger); err != nil {
		return nil, err
	}
	i.NAI = fmt.Sprintf("<EMAIL>%s.mcc%s.3gppnetwork.org", imsi, imsi.MNC(), imsi.MCC())
	i.logger.Info("[Swu IKEv2] client created",
		"imei", i.IMEI.String(),
		"imsi", imsi.String(),
		"nai", i.NAI,
		"epdg_address", i.ePDGAddress,
	)
	return &i, nil
}

func (i *IKEv2) Listen() error {
	if err := i.network.Dial(); err != nil {
		return err
	}
	i.network.ListenPacket(i.handlePacket)
	return nil
}

func (i *IKEv2) handlePacket(from NetworkType, data []byte) error {
	i.logger.Debug("[Swu IKEv2] received packet", "from", from, "size", len(data))
	switch from {
	case NetworkTypeNAT:
		i.logger.Debug("[Swu IKEv2] handling NAT packet")
	case NetworkTypeNATT:
		i.logger.Debug("[Swu IKEv2] handling NAT-T packet")
	default:
		i.logger.Error("[Swu IKEv2] unknown packet type", "type", from)
		return nil
	}
	return nil
}
