package swu

import (
	"context"
	"fmt"
	"log/slog"

	"github.com/damonto/vowifi/usim"
)

type IKEv2 struct {
	USIM        *usim.USIM
	IMEI        usim.IMEI
	ePDGAddress string
	NAI         string
	ctx         context.Context

	logger *slog.Logger
	client *SWuClient
}

func NewIKEv2(ctx context.Context, usim *usim.USIM, imei usim.IMEI, address string, logger *slog.Logger) (*IKEv2, error) {
	i := IKEv2{USIM: usim, IMEI: imei, logger: logger, ePDGAddress: address, ctx: ctx}
	imsi, err := usim.GetIMSI()
	if err != nil {
		return nil, fmt.Errorf("failed to get IMSI: %w", err)
	}
	if i.ePDGAddress == "" {
		i.ePDGAddress = fmt.Sprintf("epdg.epc.mnc%s.mcc%s.pub.3gppnetwork.org", imsi.MNC(), imsi.MCC())
		i.logger.Info("[Swu IKEv2] using default ePDG address", "address", i.ePDGAddress)
	} else {
		i.logger.Info("[Swu IKEv2] using provided ePDG address", "address", i.ePDGAddress)
	}

	// Create SWu client
	i.client, err = NewSWuClient(ctx, imei.String(), usim, i.ePDGAddress, 0, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create SWu client: %w", err)
	}

	i.NAI = fmt.Sprintf("<EMAIL>%s.mcc%s.3gppnetwork.org", imsi, imsi.MNC(), imsi.MCC())
	i.logger.Info("[Swu IKEv2] client created",
		"imei", i.IMEI.String(),
		"imsi", imsi.String(),
		"nai", i.NAI,
		"epdg_address", i.ePDGAddress,
	)
	return &i, nil
}

func (i *IKEv2) Listen() error {
	return i.client.Run()
}

func (i *IKEv2) GetClientAddress() string {
	if i.client != nil {
		return i.client.GetClientAddress()
	}
	return ""
}

func (i *IKEv2) GetPCSCFAddress() string {
	if i.client != nil {
		return i.client.GetPCSCFAddress()
	}
	return ""
}

func (i *IKEv2) Close() error {
	if i.client != nil {
		return i.client.Close()
	}
	return nil
}
