package swu

import (
	"fmt"
	"log/slog"
	"net"
	"runtime"
	"time"

	"github.com/songgao/water"
	"github.com/vishvananda/netlink"
)

// TunnelManager handles cross-platform tunnel creation and management
type TunnelManager struct {
	logger     *slog.Logger
	tunDevice  *water.Interface
	localIP    net.IP
	remoteIP   net.IP
	pcscfIP    net.IP
	tunnelName string
}

// NewTunnelManager creates a new tunnel manager
func NewTunnelManager(logger *slog.Logger) *TunnelManager {
	return &TunnelManager{
		logger: logger,
	}
}

// CreateTunnel creates a cross-platform tunnel interface
func (tm *TunnelManager) CreateTunnel(localIP, remoteIP, pcscfIP string) error {
	tm.logger.Info("[TunnelManager] creating cross-platform tunnel",
		"local_ip", localIP,
		"remote_ip", remoteIP,
		"pcscf_ip", pcscfIP,
		"platform", runtime.GOOS)

	// Parse IP addresses
	tm.localIP = net.ParseIP(localIP)
	tm.remoteIP = net.ParseIP(remoteIP)
	tm.pcscfIP = net.ParseIP(pcscfIP)

	if tm.localIP == nil || tm.remoteIP == nil {
		return fmt.Errorf("invalid IP addresses: local=%s, remote=%s", localIP, remoteIP)
	}

	// Create platform-specific tunnel
	switch runtime.GOOS {
	case "linux":
		return tm.createLinuxTunnel()
	case "darwin":
		return tm.createDarwinTunnel()
	case "windows":
		return tm.createWindowsTunnel()
	default:
		return tm.createGenericTunnel()
	}
}

// createLinuxTunnel creates a Linux-specific tunnel using netlink
func (tm *TunnelManager) createLinuxTunnel() error {
	tm.logger.Info("[TunnelManager] creating Linux tunnel using netlink")

	// Create TUN interface
	config := water.Config{
		DeviceType: water.TUN,
	}
	config.Name = "vowifi0"

	iface, err := water.New(config)
	if err != nil {
		return fmt.Errorf("failed to create TUN interface: %w", err)
	}

	tm.tunDevice = iface
	tm.tunnelName = iface.Name()

	tm.logger.Info("[TunnelManager] TUN interface created", "name", tm.tunnelName)

	// Configure interface using netlink (Linux-specific but more reliable than ip commands)
	return tm.configureLinuxInterface()
}

// configureLinuxInterface configures the Linux interface using netlink
func (tm *TunnelManager) configureLinuxInterface() error {
	// Get the interface by name
	link, err := netlink.LinkByName(tm.tunnelName)
	if err != nil {
		return fmt.Errorf("failed to get interface %s: %w", tm.tunnelName, err)
	}

	// Set interface up
	if err := netlink.LinkSetUp(link); err != nil {
		return fmt.Errorf("failed to set interface up: %w", err)
	}

	// Add IP address
	addr, err := netlink.ParseAddr(tm.localIP.String() + "/32")
	if err != nil {
		return fmt.Errorf("failed to parse address: %w", err)
	}

	if err := netlink.AddrAdd(link, addr); err != nil {
		return fmt.Errorf("failed to add address: %w", err)
	}

	// Add route to P-CSCF
	if tm.pcscfIP != nil {
		route := &netlink.Route{
			LinkIndex: link.Attrs().Index,
			Dst:       &net.IPNet{IP: tm.pcscfIP, Mask: net.CIDRMask(32, 32)},
			Src:       tm.localIP,
		}

		if err := netlink.RouteAdd(route); err != nil {
			tm.logger.Warn("[TunnelManager] failed to add route to P-CSCF", "error", err)
		}
	}

	tm.logger.Info("[TunnelManager] Linux tunnel configured successfully")
	return nil
}

// createDarwinTunnel creates a macOS-specific tunnel
func (tm *TunnelManager) createDarwinTunnel() error {
	tm.logger.Info("[TunnelManager] creating macOS tunnel")

	config := water.Config{
		DeviceType: water.TUN,
	}

	iface, err := water.New(config)
	if err != nil {
		return fmt.Errorf("failed to create TUN interface: %w", err)
	}

	tm.tunDevice = iface
	tm.tunnelName = iface.Name()

	// Configure using system commands (macOS doesn't have netlink)
	return tm.configureDarwinInterface()
}

// configureDarwinInterface configures macOS interface using ifconfig
func (tm *TunnelManager) configureDarwinInterface() error {
	// This would use exec.Command to run ifconfig commands
	// For now, just log that it's not fully implemented
	tm.logger.Warn("[TunnelManager] macOS tunnel configuration not fully implemented")
	return nil
}

// createWindowsTunnel creates a Windows-specific tunnel
func (tm *TunnelManager) createWindowsTunnel() error {
	tm.logger.Info("[TunnelManager] creating Windows tunnel")

	config := water.Config{
		DeviceType: water.TUN,
	}

	iface, err := water.New(config)
	if err != nil {
		return fmt.Errorf("failed to create TUN interface: %w", err)
	}

	tm.tunDevice = iface
	tm.tunnelName = iface.Name()

	tm.logger.Warn("[TunnelManager] Windows tunnel configuration not fully implemented")
	return nil
}

// createGenericTunnel creates a generic tunnel for unsupported platforms
func (tm *TunnelManager) createGenericTunnel() error {
	tm.logger.Info("[TunnelManager] creating generic tunnel")

	config := water.Config{
		DeviceType: water.TUN,
	}

	iface, err := water.New(config)
	if err != nil {
		return fmt.Errorf("failed to create TUN interface: %w", err)
	}

	tm.tunDevice = iface
	tm.tunnelName = iface.Name()

	tm.logger.Info("[TunnelManager] generic tunnel created", "name", tm.tunnelName)
	return nil
}

// StartPacketForwarding starts forwarding packets through the tunnel
func (tm *TunnelManager) StartPacketForwarding() error {
	if tm.tunDevice == nil {
		return fmt.Errorf("tunnel device not created")
	}

	tm.logger.Info("[TunnelManager] starting packet forwarding")

	// Start packet forwarding in a goroutine
	go tm.forwardPackets()

	return nil
}

// forwardPackets handles packet forwarding
func (tm *TunnelManager) forwardPackets() {
	buffer := make([]byte, 1500) // MTU size

	for {
		n, err := tm.tunDevice.Read(buffer)
		if err != nil {
			tm.logger.Error("[TunnelManager] failed to read from tunnel", "error", err)
			time.Sleep(100 * time.Millisecond)
			continue
		}

		tm.logger.Debug("[TunnelManager] received packet", "size", n)

		// Here you would implement packet processing and forwarding
		// For now, just log the packet
		tm.logger.Debug("[TunnelManager] packet data", "data", fmt.Sprintf("%x", buffer[:n]))
	}
}

// Close closes the tunnel
func (tm *TunnelManager) Close() error {
	if tm.tunDevice != nil {
		tm.logger.Info("[TunnelManager] closing tunnel", "name", tm.tunnelName)
		return tm.tunDevice.Close()
	}
	return nil
}

// GetTunnelName returns the tunnel interface name
func (tm *TunnelManager) GetTunnelName() string {
	return tm.tunnelName
}

// GetLocalIP returns the local IP address
func (tm *TunnelManager) GetLocalIP() net.IP {
	return tm.localIP
}

// GetRemoteIP returns the remote IP address  
func (tm *TunnelManager) GetRemoteIP() net.IP {
	return tm.remoteIP
}

// GetPCSCFIP returns the P-CSCF IP address
func (tm *TunnelManager) GetPCSCFIP() net.IP {
	return tm.pcscfIP
}
