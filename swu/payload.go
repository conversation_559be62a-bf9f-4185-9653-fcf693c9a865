package swu

const (
	IdentificationPayloadTypeFQDN   = 2
	IdentificationPayloadTypeRFC822 = 3
)

type IdentificationPayload struct {
	Type uint8
	NAI  string
}

func (i *IdentificationPayload) MarshalBinary() ([]byte, error) {
	buf := make([]byte, 4+len(i.NAI))
	buf[0] = i.Type      // type: 1 byte
	buf[1] = 0           // reserved
	buf[2] = 0           // reserved
	buf[3] = 0           // reserved
	copy(buf[4:], i.NAI) // copy data after header
	return buf, nil
}

func (i *IdentificationPayload) UnmarshalBinary(data []byte) error {
	i.Type = data[0]
	i.NAI = string(data[4:])
	return nil
}
