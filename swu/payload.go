package swu

import (
	"encoding/binary"
	"fmt"
)

// Identification payload types
const (
	IdentificationPayloadTypeFQDN   = 2
	IdentificationPayloadTypeRFC822 = 3
)

// Transform types
const (
	TransformTypeEncr  = 1
	TransformTypeInteg = 3
	TransformTypePRF   = 2
	TransformTypeDH    = 4
)

// IdentificationPayload represents an ID payload
type IdentificationPayload struct {
	IDType uint8
	NAI    string
}

func (i *IdentificationPayload) Type() uint8 {
	return PayloadTypeIdentificationInitiator
}

func (i *IdentificationPayload) Marshal() ([]byte, error) {
	buf := make([]byte, 4+len(i.NAI))
	buf[0] = i.IDType    // ID type: 1 byte
	buf[1] = 0           // reserved
	buf[2] = 0           // reserved
	buf[3] = 0           // reserved
	copy(buf[4:], i.NAI) // copy NAI data
	return buf, nil
}

func (i *IdentificationPayload) Unmarshal(data []byte) error {
	if len(data) < 4 {
		return fmt.Errorf("identification payload too short")
	}
	i.IDType = data[0]
	i.NAI = string(data[4:])
	return nil
}

// NoncePayload represents a nonce payload
type NoncePayload struct {
	Data []byte
}

func (n *NoncePayload) Type() uint8 {
	return PayloadTypeNonce
}

func (n *NoncePayload) Marshal() ([]byte, error) {
	return n.Data, nil
}

func (n *NoncePayload) Unmarshal(data []byte) error {
	n.Data = make([]byte, len(data))
	copy(n.Data, data)
	return nil
}

// KeyExchangePayload represents a key exchange payload
type KeyExchangePayload struct {
	DHGroup uint16
	Data    []byte
}

func (k *KeyExchangePayload) Type() uint8 {
	return PayloadTypeKeyExchange
}

func (k *KeyExchangePayload) Marshal() ([]byte, error) {
	buf := make([]byte, 4+len(k.Data))
	binary.BigEndian.PutUint16(buf[0:2], k.DHGroup)
	// bytes 2-3 are reserved
	copy(buf[4:], k.Data)
	return buf, nil
}

func (k *KeyExchangePayload) Unmarshal(data []byte) error {
	if len(data) < 4 {
		return fmt.Errorf("key exchange payload too short")
	}
	k.DHGroup = binary.BigEndian.Uint16(data[0:2])
	k.Data = make([]byte, len(data)-4)
	copy(k.Data, data[4:])
	return nil
}

// NotifyPayload represents a notify payload
type NotifyPayload struct {
	ProtocolID uint8
	SPISize    uint8
	NotifyType uint16
	SPI        []byte
	Data       []byte
}

func (n *NotifyPayload) Type() uint8 {
	return PayloadTypeNotify
}

func (n *NotifyPayload) Marshal() ([]byte, error) {
	buf := make([]byte, 4+len(n.SPI)+len(n.Data))
	buf[0] = n.ProtocolID
	buf[1] = n.SPISize
	binary.BigEndian.PutUint16(buf[2:4], n.NotifyType)
	copy(buf[4:], n.SPI)
	copy(buf[4+len(n.SPI):], n.Data)
	return buf, nil
}

func (n *NotifyPayload) Unmarshal(data []byte) error {
	if len(data) < 4 {
		return fmt.Errorf("notify payload too short")
	}
	n.ProtocolID = data[0]
	n.SPISize = data[1]
	n.NotifyType = binary.BigEndian.Uint16(data[2:4])

	if len(data) < 4+int(n.SPISize) {
		return fmt.Errorf("notify payload SPI too short")
	}

	n.SPI = make([]byte, n.SPISize)
	copy(n.SPI, data[4:4+n.SPISize])

	n.Data = make([]byte, len(data)-4-int(n.SPISize))
	copy(n.Data, data[4+n.SPISize:])

	return nil
}

// ConfigurationPayload represents a configuration payload
type ConfigurationPayload struct {
	ConfigType uint8
	Attributes []ConfigAttribute
}

type ConfigAttribute struct {
	Type   uint16
	Length uint16
	Value  []byte
}

func (c *ConfigurationPayload) Type() uint8 {
	return PayloadTypeConfiguration
}

func (c *ConfigurationPayload) Marshal() ([]byte, error) {
	buf := make([]byte, 4) // Header: type + reserved
	buf[0] = c.ConfigType
	// bytes 1-3 are reserved

	for _, attr := range c.Attributes {
		attrBuf := make([]byte, 4+len(attr.Value))
		binary.BigEndian.PutUint16(attrBuf[0:2], attr.Type)
		binary.BigEndian.PutUint16(attrBuf[2:4], uint16(len(attr.Value)))
		copy(attrBuf[4:], attr.Value)
		buf = append(buf, attrBuf...)
	}

	return buf, nil
}

func (c *ConfigurationPayload) Unmarshal(data []byte) error {
	if len(data) < 4 {
		return fmt.Errorf("configuration payload too short")
	}

	c.ConfigType = data[0]
	c.Attributes = make([]ConfigAttribute, 0)

	offset := 4
	for offset < len(data) {
		if offset+4 > len(data) {
			break
		}

		attr := ConfigAttribute{}
		attr.Type = binary.BigEndian.Uint16(data[offset : offset+2])
		attr.Length = binary.BigEndian.Uint16(data[offset+2 : offset+4])

		if offset+4+int(attr.Length) > len(data) {
			return fmt.Errorf("configuration attribute extends beyond payload")
		}

		attr.Value = make([]byte, attr.Length)
		copy(attr.Value, data[offset+4:offset+4+int(attr.Length)])

		c.Attributes = append(c.Attributes, attr)
		offset += 4 + int(attr.Length)
	}

	return nil
}

// ExtensibleAuthenticationPayload represents an EAP payload
type ExtensibleAuthenticationPayload struct {
	Data []byte
}

func (e *ExtensibleAuthenticationPayload) Type() uint8 {
	return PayloadTypeExtensibleAuthentication
}

func (e *ExtensibleAuthenticationPayload) Marshal() ([]byte, error) {
	return e.Data, nil
}

func (e *ExtensibleAuthenticationPayload) Unmarshal(data []byte) error {
	e.Data = make([]byte, len(data))
	copy(e.Data, data)
	return nil
}

// SecurityAssociationPayload represents a security association payload
type SecurityAssociationPayload struct {
	Proposals []Proposal
}

type Proposal struct {
	ProposalNumber uint8
	ProtocolID     uint8
	SPISize        uint8
	NumTransforms  uint8
	SPI            []byte
	Transforms     []Transform
}

type Transform struct {
	TransformType uint8
	TransformID   uint16
	Attributes    []TransformAttribute
}

type TransformAttribute struct {
	Type   uint16
	Length uint16
	Value  []byte
}

func (s *SecurityAssociationPayload) Type() uint8 {
	return PayloadTypeSecurityAssociation
}

func (s *SecurityAssociationPayload) Marshal() ([]byte, error) {
	var buf []byte

	for _, proposal := range s.Proposals {
		propBuf := make([]byte, 8+len(proposal.SPI)) // Basic proposal header
		propBuf[0] = 0                               // Last proposal flag (will be set later)
		propBuf[1] = 0                               // Reserved
		// Proposal length will be calculated later
		propBuf[4] = proposal.ProposalNumber
		propBuf[5] = proposal.ProtocolID
		propBuf[6] = proposal.SPISize
		propBuf[7] = uint8(len(proposal.Transforms))
		copy(propBuf[8:], proposal.SPI)

		// Marshal transforms
		var transformsBuf []byte
		for _, transform := range proposal.Transforms {
			transformBuf := make([]byte, 8) // Basic transform header
			transformBuf[0] = 0             // Last transform flag (will be set later)
			transformBuf[1] = 0             // Reserved
			// Transform length will be calculated later
			transformBuf[4] = transform.TransformType
			transformBuf[5] = 0 // Reserved
			binary.BigEndian.PutUint16(transformBuf[6:8], transform.TransformID)

			// Marshal attributes
			var attrsBuf []byte
			for _, attr := range transform.Attributes {
				attrBuf := make([]byte, 4+len(attr.Value))
				binary.BigEndian.PutUint16(attrBuf[0:2], attr.Type)
				binary.BigEndian.PutUint16(attrBuf[2:4], uint16(len(attr.Value)))
				copy(attrBuf[4:], attr.Value)
				attrsBuf = append(attrsBuf, attrBuf...)
			}

			// Set transform length
			transformLen := len(transformBuf) + len(attrsBuf)
			binary.BigEndian.PutUint16(transformBuf[2:4], uint16(transformLen))

			transformBuf = append(transformBuf, attrsBuf...)
			transformsBuf = append(transformsBuf, transformBuf...)
		}

		// Set last transform flag
		if len(transformsBuf) > 0 && len(proposal.Transforms) > 0 {
			// The last transform should have the "last" flag set to 0
			// This is a simplified approach - in a full implementation we'd track the exact position
		}

		// Set proposal length
		propLen := len(propBuf) + len(transformsBuf)
		binary.BigEndian.PutUint16(propBuf[2:4], uint16(propLen))

		propBuf = append(propBuf, transformsBuf...)
		buf = append(buf, propBuf...)
	}

	// Set last proposal flag
	if len(buf) > 0 && len(s.Proposals) > 0 {
		// Find the start of the last proposal and set the flag
		buf[0] = 0 // No more proposals
	}

	return buf, nil
}

func (s *SecurityAssociationPayload) Unmarshal(data []byte) error {
	s.Proposals = make([]Proposal, 0)

	offset := 0
	for offset < len(data) {
		if offset+8 > len(data) {
			break
		}

		proposal := Proposal{}
		lastProposal := data[offset] == 0
		// Skip reserved byte
		propLength := binary.BigEndian.Uint16(data[offset+2 : offset+4])
		proposal.ProposalNumber = data[offset+4]
		proposal.ProtocolID = data[offset+5]
		proposal.SPISize = data[offset+6]
		proposal.NumTransforms = data[offset+7]

		if offset+8+int(proposal.SPISize) > len(data) {
			return fmt.Errorf("proposal SPI extends beyond payload")
		}

		proposal.SPI = make([]byte, proposal.SPISize)
		copy(proposal.SPI, data[offset+8:offset+8+int(proposal.SPISize)])

		// Parse transforms
		transformOffset := offset + 8 + int(proposal.SPISize)
		proposal.Transforms = make([]Transform, 0)

		for transformOffset < offset+int(propLength) {
			if transformOffset+8 > len(data) {
				break
			}

			transform := Transform{}
			lastTransform := data[transformOffset] == 0
			// Skip reserved byte
			transformLength := binary.BigEndian.Uint16(data[transformOffset+2 : transformOffset+4])
			transform.TransformType = data[transformOffset+4]
			// Skip reserved byte
			transform.TransformID = binary.BigEndian.Uint16(data[transformOffset+6 : transformOffset+8])

			// Parse attributes
			attrOffset := transformOffset + 8
			transform.Attributes = make([]TransformAttribute, 0)

			for attrOffset < transformOffset+int(transformLength) {
				if attrOffset+4 > len(data) {
					break
				}

				attr := TransformAttribute{}
				attr.Type = binary.BigEndian.Uint16(data[attrOffset : attrOffset+2])
				attr.Length = binary.BigEndian.Uint16(data[attrOffset+2 : attrOffset+4])

				if attrOffset+4+int(attr.Length) > len(data) {
					break
				}

				attr.Value = make([]byte, attr.Length)
				copy(attr.Value, data[attrOffset+4:attrOffset+4+int(attr.Length)])

				transform.Attributes = append(transform.Attributes, attr)
				attrOffset += 4 + int(attr.Length)
			}

			proposal.Transforms = append(proposal.Transforms, transform)
			transformOffset += int(transformLength)

			if lastTransform {
				break
			}
		}

		s.Proposals = append(s.Proposals, proposal)
		offset += int(propLength)

		if lastProposal {
			break
		}
	}

	return nil
}
