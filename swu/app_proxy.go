package swu

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"net"
	"sync"
	"time"
)

// AppProxy implements an application-layer proxy for VoWiFi traffic
// This is the most cross-platform solution that doesn't require system-level changes
type AppProxy struct {
	logger *slog.Logger
	ctx    context.Context
	cancel context.CancelFunc
	
	// Configuration
	localIP  string
	remoteIP string
	pcscfIP  string
	
	// Proxy listeners
	sipListener net.Listener
	rtpListener net.Listener
	
	// Connection tracking
	connections map[string]net.Conn
	connMutex   sync.RWMutex
	
	// State
	running bool
	mutex   sync.RWMutex
}

// NewAppProxy creates a new application-layer proxy
func NewAppProxy(logger *slog.Logger) *AppProxy {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &AppProxy{
		logger:      logger,
		ctx:         ctx,
		cancel:      cancel,
		connections: make(map[string]net.Conn),
	}
}

// Configure sets up the proxy with tunnel parameters
func (proxy *AppProxy) Configure(localIP, remoteIP, pcscfIP string) error {
	proxy.mutex.Lock()
	defer proxy.mutex.Unlock()
	
	proxy.logger.Info("[AppProxy] configuring proxy",
		"local_ip", localIP,
		"remote_ip", remoteIP,
		"pcscf_ip", pcscfIP)
	
	proxy.localIP = localIP
	proxy.remoteIP = remoteIP
	proxy.pcscfIP = pcscfIP
	
	return nil
}

// Start starts the proxy listeners
func (proxy *AppProxy) Start() error {
	proxy.mutex.Lock()
	defer proxy.mutex.Unlock()
	
	if proxy.running {
		return fmt.Errorf("proxy already running")
	}
	
	proxy.logger.Info("[AppProxy] starting proxy listeners")
	
	// Start SIP proxy (port 5060)
	if err := proxy.startSIPProxy(); err != nil {
		return fmt.Errorf("failed to start SIP proxy: %w", err)
	}
	
	// Start RTP proxy (dynamic port range)
	if err := proxy.startRTPProxy(); err != nil {
		proxy.stopSIPProxy()
		return fmt.Errorf("failed to start RTP proxy: %w", err)
	}
	
	proxy.running = true
	proxy.logger.Info("[AppProxy] proxy started successfully")
	
	return nil
}

// startSIPProxy starts the SIP proxy listener
func (proxy *AppProxy) startSIPProxy() error {
	listener, err := net.Listen("tcp", ":5060")
	if err != nil {
		// Try alternative port if 5060 is busy
		listener, err = net.Listen("tcp", ":0")
		if err != nil {
			return fmt.Errorf("failed to create SIP listener: %w", err)
		}
	}
	
	proxy.sipListener = listener
	
	// Start accepting connections
	go proxy.handleSIPConnections()
	
	proxy.logger.Info("[AppProxy] SIP proxy listening", "address", listener.Addr().String())
	return nil
}

// startRTPProxy starts the RTP proxy listener
func (proxy *AppProxy) startRTPProxy() error {
	listener, err := net.Listen("tcp", ":0") // Use dynamic port
	if err != nil {
		return fmt.Errorf("failed to create RTP listener: %w", err)
	}
	
	proxy.rtpListener = listener
	
	// Start accepting connections
	go proxy.handleRTPConnections()
	
	proxy.logger.Info("[AppProxy] RTP proxy listening", "address", listener.Addr().String())
	return nil
}

// handleSIPConnections handles incoming SIP connections
func (proxy *AppProxy) handleSIPConnections() {
	for {
		select {
		case <-proxy.ctx.Done():
			return
		default:
		}
		
		conn, err := proxy.sipListener.Accept()
		if err != nil {
			if proxy.isRunning() {
				proxy.logger.Error("[AppProxy] failed to accept SIP connection", "error", err)
			}
			continue
		}
		
		go proxy.handleSIPConnection(conn)
	}
}

// handleRTPConnections handles incoming RTP connections
func (proxy *AppProxy) handleRTPConnections() {
	for {
		select {
		case <-proxy.ctx.Done():
			return
		default:
		}
		
		conn, err := proxy.rtpListener.Accept()
		if err != nil {
			if proxy.isRunning() {
				proxy.logger.Error("[AppProxy] failed to accept RTP connection", "error", err)
			}
			continue
		}
		
		go proxy.handleRTPConnection(conn)
	}
}

// handleSIPConnection handles a single SIP connection
func (proxy *AppProxy) handleSIPConnection(clientConn net.Conn) {
	defer clientConn.Close()
	
	clientAddr := clientConn.RemoteAddr().String()
	proxy.logger.Info("[AppProxy] new SIP connection", "client", clientAddr)
	
	// Connect to P-CSCF
	pcscfAddr := fmt.Sprintf("%s:5060", proxy.pcscfIP)
	serverConn, err := net.DialTimeout("tcp", pcscfAddr, 10*time.Second)
	if err != nil {
		proxy.logger.Error("[AppProxy] failed to connect to P-CSCF", "error", err, "pcscf", pcscfAddr)
		return
	}
	defer serverConn.Close()
	
	proxy.logger.Info("[AppProxy] connected to P-CSCF", "pcscf", pcscfAddr)
	
	// Store connection for tracking
	proxy.connMutex.Lock()
	proxy.connections[clientAddr] = clientConn
	proxy.connMutex.Unlock()
	
	defer func() {
		proxy.connMutex.Lock()
		delete(proxy.connections, clientAddr)
		proxy.connMutex.Unlock()
	}()
	
	// Start bidirectional forwarding
	go proxy.forwardData(clientConn, serverConn, "client->server")
	proxy.forwardData(serverConn, clientConn, "server->client")
}

// handleRTPConnection handles a single RTP connection
func (proxy *AppProxy) handleRTPConnection(clientConn net.Conn) {
	defer clientConn.Close()
	
	clientAddr := clientConn.RemoteAddr().String()
	proxy.logger.Info("[AppProxy] new RTP connection", "client", clientAddr)
	
	// For RTP, we might need to determine the target dynamically
	// For now, just log and close
	proxy.logger.Info("[AppProxy] RTP connection handling not fully implemented")
}

// forwardData forwards data between two connections
func (proxy *AppProxy) forwardData(src, dst net.Conn, direction string) {
	defer func() {
		if r := recover(); r != nil {
			proxy.logger.Error("[AppProxy] panic in forwardData", "direction", direction, "panic", r)
		}
	}()
	
	buffer := make([]byte, 4096)
	totalBytes := 0
	
	for {
		src.SetReadDeadline(time.Now().Add(30 * time.Second))
		n, err := src.Read(buffer)
		if err != nil {
			if err != io.EOF {
				proxy.logger.Debug("[AppProxy] read error", "direction", direction, "error", err)
			}
			break
		}
		
		if n > 0 {
			dst.SetWriteDeadline(time.Now().Add(10 * time.Second))
			_, err := dst.Write(buffer[:n])
			if err != nil {
				proxy.logger.Debug("[AppProxy] write error", "direction", direction, "error", err)
				break
			}
			
			totalBytes += n
			proxy.logger.Debug("[AppProxy] forwarded data", "direction", direction, "bytes", n)
		}
	}
	
	proxy.logger.Info("[AppProxy] forwarding completed", "direction", direction, "total_bytes", totalBytes)
}

// isRunning returns whether the proxy is running
func (proxy *AppProxy) isRunning() bool {
	proxy.mutex.RLock()
	defer proxy.mutex.RUnlock()
	return proxy.running
}

// Stop stops the proxy
func (proxy *AppProxy) Stop() error {
	proxy.mutex.Lock()
	defer proxy.mutex.Unlock()
	
	if !proxy.running {
		return nil
	}
	
	proxy.logger.Info("[AppProxy] stopping proxy")
	
	// Cancel context
	proxy.cancel()
	
	// Close listeners
	proxy.stopSIPProxy()
	proxy.stopRTPProxy()
	
	// Close all active connections
	proxy.connMutex.Lock()
	for addr, conn := range proxy.connections {
		proxy.logger.Debug("[AppProxy] closing connection", "client", addr)
		conn.Close()
	}
	proxy.connections = make(map[string]net.Conn)
	proxy.connMutex.Unlock()
	
	proxy.running = false
	proxy.logger.Info("[AppProxy] proxy stopped")
	
	return nil
}

// stopSIPProxy stops the SIP proxy listener
func (proxy *AppProxy) stopSIPProxy() {
	if proxy.sipListener != nil {
		proxy.sipListener.Close()
		proxy.sipListener = nil
	}
}

// stopRTPProxy stops the RTP proxy listener
func (proxy *AppProxy) stopRTPProxy() {
	if proxy.rtpListener != nil {
		proxy.rtpListener.Close()
		proxy.rtpListener = nil
	}
}

// GetProxyInfo returns proxy information
func (proxy *AppProxy) GetProxyInfo() map[string]interface{} {
	proxy.mutex.RLock()
	defer proxy.mutex.RUnlock()
	
	info := map[string]interface{}{
		"running":     proxy.running,
		"local_ip":    proxy.localIP,
		"remote_ip":   proxy.remoteIP,
		"pcscf_ip":    proxy.pcscfIP,
		"connections": len(proxy.connections),
	}
	
	if proxy.sipListener != nil {
		info["sip_address"] = proxy.sipListener.Addr().String()
	}
	
	if proxy.rtpListener != nil {
		info["rtp_address"] = proxy.rtpListener.Addr().String()
	}
	
	return info
}
