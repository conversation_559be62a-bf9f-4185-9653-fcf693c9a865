package swu

import (
	"encoding/binary"
	"fmt"
	"log/slog"
)

// IKE Exchange Types
const (
	ExchangeTypeIKESAInit     = 34
	ExchangeTypeIKEAuth       = 35
	ExchangeTypeInformational = 37
)

// IKE Payload Types
const (
	PayloadTypeNone                      = 0
	PayloadTypeSecurityAssociation       = 33
	PayloadTypeKeyExchange               = 34
	PayloadTypeIdentificationInitiator   = 35
	PayloadTypeIdentificationResponder   = 36
	PayloadTypeNonce                     = 40
	PayloadTypeNotify                    = 41
	PayloadTypeConfiguration             = 47
	PayloadTypeExtensibleAuthentication  = 48
	PayloadTypeEncryptedAndAuthenticated = 46
)

// GenericPacket represents a generic IKEv2 packet
type GenericPacket struct {
	// IKE Header
	SPIInitiator uint64
	SPIResponder uint64
	NextPayload  uint8
	VersionMajor uint8
	VersionMinor uint8
	ExchangeType uint8
	Flags        uint8
	MessageID    uint32
	Length       uint32

	// Payloads
	Payloads []Payload

	// Raw packet data for debugging
	RawPacket []byte
}

// Payload interface for all IKE payloads
type Payload interface {
	Type() uint8
	Marshal() ([]byte, error)
	Unmarshal(data []byte) error
}

// NewGenericPacket creates a new generic packet
func NewGenericPacket(transaction *Transaction, exchangeType uint8) *GenericPacket {
	return &GenericPacket{
		SPIInitiator: transaction.SPIInitiator,
		SPIResponder: transaction.SPIResponder,
		VersionMajor: 2,
		VersionMinor: 0,
		ExchangeType: exchangeType,
		Flags:        0x08, // Initiator flag
		MessageID:    transaction.MessageID,
		Payloads:     make([]Payload, 0),
	}
}

// AddPayload adds a payload to the packet
func (p *GenericPacket) AddPayload(payload Payload) {
	p.Payloads = append(p.Payloads, payload)
}

// Marshal serializes the packet to bytes
func (p *GenericPacket) Marshal() ([]byte, error) {
	var payloadData []byte

	// Marshal all payloads
	for i, payload := range p.Payloads {
		data, err := payload.Marshal()
		if err != nil {
			return nil, fmt.Errorf("failed to marshal payload %d: %w", i, err)
		}

		// Add payload header
		payloadHeader := make([]byte, 4)
		if i < len(p.Payloads)-1 {
			payloadHeader[0] = p.Payloads[i+1].Type() // Next payload type
		} else {
			payloadHeader[0] = 0 // No next payload
		}
		payloadHeader[1] = 0                                                // Critical bit and reserved
		binary.BigEndian.PutUint16(payloadHeader[2:4], uint16(len(data)+4)) // Payload length

		payloadData = append(payloadData, payloadHeader...)
		payloadData = append(payloadData, data...)
	}

	// Set next payload type in header
	if len(p.Payloads) > 0 {
		p.NextPayload = p.Payloads[0].Type()
	} else {
		p.NextPayload = 0
	}

	// Calculate total length
	p.Length = uint32(28 + len(payloadData)) // 28 bytes for IKE header

	// Marshal IKE header
	header := make([]byte, 28)
	binary.BigEndian.PutUint64(header[0:8], p.SPIInitiator)
	binary.BigEndian.PutUint64(header[8:16], p.SPIResponder)
	header[16] = p.NextPayload
	header[17] = (p.VersionMajor << 4) | p.VersionMinor
	header[18] = p.ExchangeType
	header[19] = p.Flags
	binary.BigEndian.PutUint32(header[20:24], p.MessageID)
	binary.BigEndian.PutUint32(header[24:28], p.Length)

	// Combine header and payloads
	packet := append(header, payloadData...)
	p.RawPacket = packet

	return packet, nil
}

// Unmarshal deserializes a packet from bytes
func (p *GenericPacket) Unmarshal(data []byte) error {
	if len(data) < 28 {
		return fmt.Errorf("packet too short: %d bytes", len(data))
	}

	p.RawPacket = make([]byte, len(data))
	copy(p.RawPacket, data)

	// Parse IKE header
	p.SPIInitiator = binary.BigEndian.Uint64(data[0:8])
	p.SPIResponder = binary.BigEndian.Uint64(data[8:16])
	p.NextPayload = data[16]
	version := data[17]
	p.VersionMajor = (version & 0xF0) >> 4
	p.VersionMinor = version & 0x0F
	p.ExchangeType = data[18]
	p.Flags = data[19]
	p.MessageID = binary.BigEndian.Uint32(data[20:24])
	p.Length = binary.BigEndian.Uint32(data[24:28])

	// Validate length
	if uint32(len(data)) != p.Length {
		return fmt.Errorf("packet length mismatch: expected %d, got %d", p.Length, len(data))
	}

	// Parse payloads
	payloadData := data[28:]
	p.Payloads = make([]Payload, 0)

	nextPayloadType := p.NextPayload
	offset := 0

	for nextPayloadType != 0 && offset < len(payloadData) {
		if offset+4 > len(payloadData) {
			return fmt.Errorf("incomplete payload header at offset %d", offset)
		}

		// Parse payload header
		currentPayloadType := nextPayloadType
		nextPayloadType = payloadData[offset]
		// Skip critical bit and reserved byte
		payloadLength := binary.BigEndian.Uint16(payloadData[offset+2 : offset+4])

		if payloadLength < 4 {
			return fmt.Errorf("invalid payload length: %d", payloadLength)
		}

		if offset+int(payloadLength) > len(payloadData) {
			return fmt.Errorf("payload extends beyond packet boundary")
		}

		// Extract payload data (excluding header)
		currentPayloadData := payloadData[offset+4 : offset+int(payloadLength)]

		// Create payload based on type
		var payload Payload
		switch currentPayloadType {
		case PayloadTypeSecurityAssociation:
			payload = &SecurityAssociationPayload{}
		case PayloadTypeKeyExchange:
			payload = &KeyExchangePayload{}
		case PayloadTypeNonce:
			payload = &NoncePayload{}
		case PayloadTypeNotify:
			payload = &NotifyPayload{}
		case PayloadTypeConfiguration:
			payload = &ConfigurationPayload{}
		case PayloadTypeExtensibleAuthentication:
			payload = &ExtensibleAuthenticationPayload{}
		case PayloadTypeEncryptedAndAuthenticated:
			payload = &EncryptedAndAuthenticatedPayload{}
		case PayloadTypeIdentificationInitiator:
			payload = &IdentificationPayload{}
		case PayloadTypeIdentificationResponder:
			payload = &IdentificationPayload{}
		default:
			// Unknown payload type, create generic payload
			payload = &GenericPayload{PayloadType: currentPayloadType, Data: currentPayloadData}
		}

		if payload != nil {
			if err := payload.Unmarshal(currentPayloadData); err != nil {
				return fmt.Errorf("failed to unmarshal payload type %d: %w", currentPayloadType, err)
			}
			p.Payloads = append(p.Payloads, payload)
		}

		offset += int(payloadLength)
	}

	return nil
}

// FindPayload finds the first payload of the specified type
func (p *GenericPacket) FindPayload(payloadType uint8) Payload {
	for _, payload := range p.Payloads {
		if payload.Type() == payloadType {
			return payload
		}
	}
	return nil
}

// FindAllPayloads finds all payloads of the specified type
func (p *GenericPacket) FindAllPayloads(payloadType uint8) []Payload {
	var result []Payload
	for _, payload := range p.Payloads {
		if payload.Type() == payloadType {
			result = append(result, payload)
		}
	}
	return result
}

// GenericPayload represents an unknown payload type
type GenericPayload struct {
	PayloadType uint8
	Data        []byte
}

func (g *GenericPayload) Type() uint8 {
	return g.PayloadType
}

func (g *GenericPayload) Marshal() ([]byte, error) {
	return g.Data, nil
}

func (g *GenericPayload) Unmarshal(data []byte) error {
	g.Data = make([]byte, len(data))
	copy(g.Data, data)
	return nil
}

// UnmarshalGenericPacket creates a GenericPacket from raw bytes
func UnmarshalGenericPacket(data []byte, logger *slog.Logger) (*GenericPacket, error) {
	packet := &GenericPacket{}
	if err := packet.Unmarshal(data); err != nil {
		return nil, fmt.Errorf("failed to unmarshal packet: %w", err)
	}

	logger.Debug("[Packet] unmarshaled packet",
		"spi_initiator", fmt.Sprintf("0x%016x", packet.SPIInitiator),
		"spi_responder", fmt.Sprintf("0x%016x", packet.SPIResponder),
		"exchange_type", packet.ExchangeType,
		"message_id", packet.MessageID,
		"payload_count", len(packet.Payloads))

	return packet, nil
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
