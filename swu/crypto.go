package swu

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/binary"
	"fmt"
	"hash"
	"log/slog"
	"math/big"
)

// Crypto algorithm constants
const (
	// Encryption algorithms
	EncrAESCBC = 12

	// Integrity algorithms
	AuthHMACMD596      = 1
	AuthHMACSHA196     = 2
	AuthHMACSHA2256128 = 12

	// PRF algorithms
	PRFHMACMD5     = 1
	PRFHMACSHA1    = 2
	PRFHMACSHA2256 = 5

	// DH Groups
	DHGroup1024MODP = 2
)

// DH Group 2 (1024-bit MODP) parameters - RFC 2409
var (
	dhGroup2Prime     = mustParseBigInt("FFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD129024E088A67CC74020BBEA63B139B22514A08798E3404DDEF9519B3CD3A431B302B0A6DF25F14374FE1356D6D51C245E485B576625E7EC6F44C42E9A637ED6B0BFF5CB6F406B7EDEE386BFB5A899FA5AE9F24117C4B1FE649286651ECE65381FFFFFFFFFFFFFFFF")
	dhGroup2Generator = big.NewInt(2)
)

func mustParseBigInt(s string) *big.Int {
	n, ok := new(big.Int).SetString(s, 16)
	if !ok {
		panic("failed to parse big int: " + s)
	}
	return n
}

// CryptoHelper manages cryptographic operations for IKEv2
type CryptoHelper struct {
	logger *slog.Logger

	// DH parameters
	dhGroup      uint16
	dhPrivateKey *big.Int
	dhPublicKey  *big.Int
	dhSharedKey  []byte

	// Crypto parameters
	prfAlg     uint16
	integAlg   uint16
	encrAlg    uint16
	encrKeyLen uint16

	// SPI values
	spiInitiator uint64
	spiResponder uint64

	// Nonces
	nonceInitiator []byte
	nonceResponder []byte

	// Derived keys
	skeyseed []byte
	skd      []byte
	skAi     []byte
	skAr     []byte
	skEi     []byte
	skEr     []byte
	skPi     []byte
	skPr     []byte
}

// NewCryptoHelper creates a new crypto helper
func NewCryptoHelper(logger *slog.Logger) *CryptoHelper {
	return &CryptoHelper{
		logger: logger,
	}
}

// SetCryptoDH sets the DH group and generates key pair
func (c *CryptoHelper) SetCryptoDH(dhGroup uint16) error {
	c.dhGroup = dhGroup

	switch dhGroup {
	case DHGroup1024MODP:
		// Generate private key (random number)
		privateKey, err := rand.Int(rand.Reader, dhGroup2Prime)
		if err != nil {
			return fmt.Errorf("failed to generate DH private key: %w", err)
		}
		c.dhPrivateKey = privateKey

		// Calculate public key: g^private mod p
		c.dhPublicKey = new(big.Int).Exp(dhGroup2Generator, c.dhPrivateKey, dhGroup2Prime)

		c.logger.Debug("[CryptoHelper] DH key pair generated", "group", dhGroup)
		return nil

	default:
		return fmt.Errorf("unsupported DH group: %d", dhGroup)
	}
}

// GetDHPublicKey returns the DH public key as bytes
func (c *CryptoHelper) GetDHPublicKey() []byte {
	if c.dhPublicKey == nil {
		return nil
	}

	// Convert to bytes with proper padding for 1024-bit group
	keyBytes := c.dhPublicKey.Bytes()

	// Pad to 128 bytes for 1024-bit group (1024 bits = 128 bytes)
	padded := make([]byte, 128)
	if len(keyBytes) <= 128 {
		copy(padded[128-len(keyBytes):], keyBytes)
		keyBytes = padded
	} else {
		// If key is longer than expected, truncate to 128 bytes
		keyBytes = keyBytes[len(keyBytes)-128:]
	}

	return keyBytes
}

// GenerateIKESecret generates the shared secret from responder's public key
func (c *CryptoHelper) GenerateIKESecret(responderPublicKey []byte) error {
	if c.dhPrivateKey == nil {
		return fmt.Errorf("DH private key not set")
	}

	// Convert responder's public key to big.Int
	responderPubKey := new(big.Int).SetBytes(responderPublicKey)

	// Calculate shared secret: responder_public^private mod p
	sharedSecret := new(big.Int).Exp(responderPubKey, c.dhPrivateKey, dhGroup2Prime).Bytes()

	// Pad to 128 bytes for 1024-bit group (1024 bits = 128 bytes)
	c.dhSharedKey = make([]byte, 128)
	if len(sharedSecret) <= 128 {
		copy(c.dhSharedKey[128-len(sharedSecret):], sharedSecret)
	} else {
		// If key is longer than expected, truncate to 128 bytes
		copy(c.dhSharedKey, sharedSecret[len(sharedSecret)-128:])
	}

	c.logger.Debug("[CryptoHelper] DH shared secret generated", "length", len(c.dhSharedKey))
	return nil
}

// SetCryptoMode sets the cryptographic algorithms
func (c *CryptoHelper) SetCryptoMode(prf, integ, encr, encrKeyLen uint16) {
	c.prfAlg = prf
	c.integAlg = integ
	c.encrAlg = encr
	c.encrKeyLen = encrKeyLen

	c.logger.Debug("[CryptoHelper] crypto mode set",
		"prf", prf,
		"integ", integ,
		"encr", encr,
		"encr_keylen", encrKeyLen)
}

// SetSPI sets the SPI values
func (c *CryptoHelper) SetSPI(initiator, responder uint64) {
	c.spiInitiator = initiator
	c.spiResponder = responder
}

// SetNonce sets the nonce values
func (c *CryptoHelper) SetNonce(initiator, responder []byte) {
	c.nonceInitiator = make([]byte, len(initiator))
	copy(c.nonceInitiator, initiator)

	c.nonceResponder = make([]byte, len(responder))
	copy(c.nonceResponder, responder)
}

// prf performs the PRF function
func (c *CryptoHelper) prf(key, data []byte) []byte {
	var h hash.Hash

	switch c.prfAlg {
	case PRFHMACMD5:
		h = hmac.New(md5.New, key)
	case PRFHMACSHA1:
		h = hmac.New(sha1.New, key)
	case PRFHMACSHA2256:
		h = hmac.New(sha256.New, key)
	default:
		c.logger.Error("[CryptoHelper] unsupported PRF algorithm", "alg", c.prfAlg)
		return nil
	}

	h.Write(data)
	return h.Sum(nil)
}

// prfPlus performs the prf+ function for key derivation
func (c *CryptoHelper) prfPlus(key []byte, seed []byte, length int) []byte {
	var result []byte
	var lastSegment []byte
	counter := byte(1)

	for len(result) < length {
		// PHP implementation: prfHmac($lastsegment . $STREAM . pack('C', $i), $KEY)
		data := append(lastSegment, seed...)
		data = append(data, counter)
		t := c.prf(key, data)
		result = append(result, t...)
		lastSegment = t
		counter++
	}

	return result[:length]
}

// DeriveKeys derives all IKE keys from the shared secret
func (c *CryptoHelper) DeriveKeys() error {
	if c.dhSharedKey == nil || c.nonceInitiator == nil || c.nonceResponder == nil {
		return fmt.Errorf("missing required data for key derivation")
	}

	// SKEYSEED = prf(g^ir, Ni | Nr) - as per PHP implementation
	nonces := append(c.nonceInitiator, c.nonceResponder...)
	c.skeyseed = c.prf(c.dhSharedKey, nonces)

	// Debug: log key derivation inputs for comparison with PHP
	c.logger.Debug("[CryptoHelper] key derivation inputs",
		"dh_key", fmt.Sprintf("%x", c.dhSharedKey),
		"nonce", fmt.Sprintf("%x", nonces),
		"skeyseed", fmt.Sprintf("%x", c.skeyseed))

	// Calculate key lengths
	var prfLen, integLen, encrLen int

	switch c.prfAlg {
	case PRFHMACMD5:
		prfLen = 16
	case PRFHMACSHA1:
		prfLen = 20
	case PRFHMACSHA2256:
		prfLen = 32
	}

	switch c.integAlg {
	case AuthHMACMD596:
		integLen = 16
	case AuthHMACSHA196:
		integLen = 20
	case AuthHMACSHA2256128:
		integLen = 32
	}

	encrLen = int(c.encrKeyLen / 8) // Convert bits to bytes

	// Key derivation seed: Ni | Nr | SPIi | SPIr (as per PHP implementation)
	seed := make([]byte, len(nonces)+16) // nonces + 8 bytes SPIi + 8 bytes SPIr
	copy(seed, nonces)

	// Add SPIi and SPIr (8 bytes each, big endian)
	spiOffset := len(nonces)
	binary.BigEndian.PutUint64(seed[spiOffset:spiOffset+8], c.spiInitiator)
	binary.BigEndian.PutUint64(seed[spiOffset+8:spiOffset+16], c.spiResponder)

	// Debug: log seed for comparison with PHP STREAM
	c.logger.Debug("[CryptoHelper] key derivation seed",
		"stream", fmt.Sprintf("%x", seed),
		"spi_initiator", fmt.Sprintf("0x%016x", c.spiInitiator),
		"spi_responder", fmt.Sprintf("0x%016x", c.spiResponder))

	// Total key material needed
	totalLen := encrLen*2 + integLen*2 + prfLen*2
	keyMaterial := c.prfPlus(c.skeyseed, seed, totalLen)

	// Extract individual keys
	offset := 0
	c.skd = keyMaterial[offset : offset+prfLen]
	offset += prfLen

	c.skAi = keyMaterial[offset : offset+integLen]
	offset += integLen

	c.skAr = keyMaterial[offset : offset+integLen]
	offset += integLen

	c.skEi = keyMaterial[offset : offset+encrLen]
	offset += encrLen

	c.skEr = keyMaterial[offset : offset+encrLen]
	offset += encrLen

	c.skPi = keyMaterial[offset : offset+prfLen]
	offset += prfLen

	c.skPr = keyMaterial[offset : offset+prfLen]

	c.logger.Info("[CryptoHelper] keys derived successfully",
		"skd_len", len(c.skd),
		"sk_ai_len", len(c.skAi),
		"sk_ar_len", len(c.skAr),
		"sk_ei_len", len(c.skEi),
		"sk_er_len", len(c.skEr),
		"sk_pi_len", len(c.skPi),
		"sk_pr_len", len(c.skPr))

	// Debug: log key values for comparison with PHP
	c.logger.Debug("[CryptoHelper] derived keys",
		"skeyseed", fmt.Sprintf("%x", c.skeyseed),
		"sk_ai", fmt.Sprintf("%x", c.skAi),
		"sk_ar", fmt.Sprintf("%x", c.skAr),
		"sk_ei", fmt.Sprintf("%x", c.skEi),
		"sk_er", fmt.Sprintf("%x", c.skEr))

	return nil
}

// EAP performs EAP-AKA key derivation
func (c *CryptoHelper) EAP(identity string, res, ik, ck []byte) error {
	// This is a simplified EAP-AKA implementation
	// In a full implementation, this would derive EAP-AKA specific keys
	c.logger.Info("[CryptoHelper] EAP-AKA key derivation",
		"identity", identity,
		"res_len", len(res),
		"ik_len", len(ik),
		"ck_len", len(ck))

	return nil
}

// EAPDecrypt decrypts EAP encrypted data
func (c *CryptoHelper) EAPDecrypt(iv, encryptedData []byte) ([]byte, error) {
	// This is a placeholder for EAP-AKA decryption
	// In a full implementation, this would perform AES decryption
	c.logger.Debug("[CryptoHelper] EAP decrypt", "iv_len", len(iv), "data_len", len(encryptedData))
	return encryptedData, nil
}

// GetIVLength returns the IV length for the current encryption algorithm
func (c *CryptoHelper) GetIVLength() int {
	switch c.encrAlg {
	case EncrAESCBC:
		return 16 // AES block size
	default:
		return 16 // Default to AES block size
	}
}

// GetIntegLength returns the integrity check length for the current algorithm
func (c *CryptoHelper) GetIntegLength() int {
	switch c.integAlg {
	case AuthHMACMD596:
		return 12 // HMAC-MD5-96 truncated to 96 bits (12 bytes)
	case AuthHMACSHA196:
		return 12 // HMAC-SHA1-96 truncated to 96 bits (12 bytes)
	case AuthHMACSHA2256128:
		return 16 // HMAC-SHA2-256-128 truncated to 128 bits (16 bytes)
	default:
		return 12 // Default to MD5 length
	}
}

// DecryptPayload decrypts an encrypted and authenticated payload
// This method needs the full IKE packet for integrity verification
func (c *CryptoHelper) DecryptPayload(encryptedData []byte, fullPacket []byte) ([]byte, error) {
	ivLength := c.GetIVLength()
	integLength := c.GetIntegLength()

	if len(encryptedData) < ivLength+integLength {
		return nil, fmt.Errorf("encrypted payload too short: %d < %d", len(encryptedData), ivLength+integLength)
	}

	// Extract IV, encrypted data, and integrity check
	iv := encryptedData[:ivLength]
	encrypted := encryptedData[ivLength : len(encryptedData)-integLength]
	ic := encryptedData[len(encryptedData)-integLength:]

	c.logger.Debug("[CryptoHelper] decrypting payload", "iv_len", len(iv), "encrypted_len", len(encrypted), "ic_len", len(ic))

	// Verify integrity check
	// The integrity check is calculated over the entire IKE packet minus the IC itself
	// According to RFC 7296, the IC covers the entire IKE message except the IC field
	verifiedPacket := fullPacket[:len(fullPacket)-integLength]

	// Try both SK_ar and SK_ai for debugging
	calculatedIC_ar := c.IntegHmac(verifiedPacket, c.skAr)
	calculatedIC_ai := c.IntegHmac(verifiedPacket, c.skAi)
	calculatedIC := calculatedIC_ar

	c.logger.Debug("[CryptoHelper] integrity check details",
		"full_packet_len", len(fullPacket),
		"verified_packet_len", len(verifiedPacket),
		"integ_length", integLength,
		"received_ic", fmt.Sprintf("%x", ic),
		"calculated_ic_ar", fmt.Sprintf("%x", calculatedIC_ar),
		"calculated_ic_ai", fmt.Sprintf("%x", calculatedIC_ai),
		"sk_ar", fmt.Sprintf("%x", c.skAr),
		"sk_ai", fmt.Sprintf("%x", c.skAi),
		"verified_packet_end", fmt.Sprintf("%x", verifiedPacket[len(verifiedPacket)-16:]))

	if !bytesEqual(ic, calculatedIC) {
		c.logger.Warn("[CryptoHelper] integrity check failed, proceeding anyway for debugging")
		// For debugging, we'll continue even if integrity check fails
		// TODO: Fix integrity check calculation
		// In production, this should return an error
	} else {
		c.logger.Info("[CryptoHelper] integrity check passed!")
	}

	// Decrypt the data using AES-CBC
	decrypted, err := c.decryptAESCBC(encrypted, c.skEr, iv)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt payload: %w", err)
	}

	c.logger.Debug("[CryptoHelper] raw decrypted data", "len", len(decrypted), "first_bytes", fmt.Sprintf("%x", decrypted[:min(16, len(decrypted))]))

	// Remove padding according to RFC 7296
	// The last byte indicates the padding length
	if len(decrypted) == 0 {
		return nil, fmt.Errorf("decrypted payload is empty")
	}

	padLength := int(decrypted[len(decrypted)-1])
	c.logger.Debug("[CryptoHelper] padding info", "pad_length", padLength, "total_len", len(decrypted))

	if padLength >= len(decrypted) || padLength < 0 {
		c.logger.Warn("[CryptoHelper] invalid padding length, using raw data", "pad_length", padLength)
		return decrypted, nil
	}

	// Remove padding and pad length byte
	decrypted = decrypted[:len(decrypted)-1-padLength]

	c.logger.Debug("[CryptoHelper] payload decrypted successfully", "decrypted_len", len(decrypted))
	return decrypted, nil
}

// bytesEqual compares two byte slices for equality
func bytesEqual(a, b []byte) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

// IntegHmac calculates HMAC for integrity verification
func (c *CryptoHelper) IntegHmac(data []byte, key []byte) []byte {
	switch c.integAlg {
	case 1: // HMAC-MD5-96
		h := hmac.New(md5.New, key)
		h.Write(data)
		return h.Sum(nil)[:12] // Truncate to 96 bits (12 bytes)
	case 2: // HMAC-SHA1-96
		h := hmac.New(sha1.New, key)
		h.Write(data)
		return h.Sum(nil)[:12] // Truncate to 96 bits (12 bytes)
	case 12: // HMAC-SHA2-256-128
		h := hmac.New(sha256.New, key)
		h.Write(data)
		return h.Sum(nil)[:16] // Truncate to 128 bits (16 bytes)
	default:
		c.logger.Error("[CryptoHelper] unsupported integrity algorithm", "integ", c.integAlg)
		return nil
	}
}

// max returns the maximum of two integers
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// decryptAESCBC performs AES-CBC decryption
func (c *CryptoHelper) decryptAESCBC(ciphertext, key, iv []byte) ([]byte, error) {
	// Import crypto/aes and crypto/cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}

	if len(ciphertext)%aes.BlockSize != 0 {
		return nil, fmt.Errorf("ciphertext is not a multiple of the block size")
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	return plaintext, nil
}

// GenerateESPSecret generates ESP keys for tunnel mode
func (c *CryptoHelper) GenerateESPSecret(encrKeyLen, integAlg uint16) (map[string][]byte, error) {
	// This is a simplified ESP key generation
	// In a full implementation, this would derive proper ESP keys
	keys := make(map[string][]byte)

	keyLen := int(encrKeyLen / 8)
	keys["sk_ei"] = make([]byte, keyLen)
	keys["sk_er"] = make([]byte, keyLen)
	keys["sk_ai"] = make([]byte, 20) // SHA1 length
	keys["sk_ar"] = make([]byte, 20)

	// Generate random keys for now
	rand.Read(keys["sk_ei"])
	rand.Read(keys["sk_er"])
	rand.Read(keys["sk_ai"])
	rand.Read(keys["sk_ar"])

	c.logger.Info("[CryptoHelper] ESP keys generated",
		"encr_keylen", encrKeyLen,
		"integ_alg", integAlg)

	return keys, nil
}
