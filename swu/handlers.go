package swu

import (
	"encoding/binary"
	"fmt"
	"net"
	"time"
)

// handleIKESAInitResponse handles the IKE_SA_INIT response
func (c *SWuClient) handleIKESAInitResponse(packet *GenericPacket, userdata any) error {
	c.logger.Info("[SWuClient] received IKE_SA_INIT response")

	// Close NAT connection, switch to NAT-T
	// (In a real implementation, we'd properly manage connection lifecycle)

	// Parse response payloads
	var nonce *NoncePayload
	var sa *SecurityAssociationPayload
	var ke *KeyExchangePayload

	for _, payload := range packet.Payloads {
		switch p := payload.(type) {
		case *NoncePayload:
			nonce = p
		case *SecurityAssociationPayload:
			sa = p
		case *KeyExchangePayload:
			ke = p
		}
	}

	if nonce == nil || sa == nil || ke == nil {
		return fmt.Errorf("missing required payloads in IKE_SA_INIT response")
	}

	// Validate DH group
	if ke.DHGroup != c.transaction.IKEDH {
		return fmt.Errorf("DH group mismatch: expected %d, got %d", c.transaction.IKEDH, ke.DHGroup)
	}

	// Extract crypto parameters from selected proposal
	if len(sa.Proposals) == 0 {
		return fmt.Errorf("no proposals in SA payload")
	}

	proposal := sa.Proposals[0]
	var encr, encrLength, integ, prf uint16

	for _, transform := range proposal.Transforms {
		switch transform.TransformType {
		case TransformTypeEncr:
			encr = transform.TransformID
			// Extract key length from attributes
			for _, attr := range transform.Attributes {
				if attr.Type == 14 { // Key Length attribute
					if len(attr.Value) >= 2 {
						encrLength = binary.BigEndian.Uint16(attr.Value)
					}
				}
			}
		case TransformTypeInteg:
			integ = transform.TransformID
		case TransformTypePRF:
			prf = transform.TransformID
		}
	}

	if encr == 0 || encrLength == 0 || integ == 0 || prf == 0 {
		return fmt.Errorf("incomplete crypto parameters")
	}

	// Set crypto parameters
	c.transaction.SetCryptoMode(encr, encrLength, integ, prf)
	c.transaction.SPIResponder = packet.SPIResponder
	c.transaction.SetCryptoSPI()
	c.transaction.NonceResponder = nonce.Data
	c.transaction.SetCryptoNonce()

	// Generate shared secret
	if err := c.transaction.Crypto.GenerateIKESecret(ke.Data); err != nil {
		return fmt.Errorf("failed to generate IKE secret: %w", err)
	}

	// Derive keys
	if err := c.transaction.Crypto.DeriveKeys(); err != nil {
		return fmt.Errorf("failed to derive keys: %w", err)
	}

	c.logger.Info("[SWuClient] IKE_SA_INIT completed, starting IKE_AUTH")

	// Increment message ID and start IKE_AUTH
	c.transaction.IncrementMessageID()

	return c.initiateIKEAuthRequest()
}

// initiateIKEAuthRequest starts the IKE_AUTH exchange with EAP-AKA
func (c *SWuClient) initiateIKEAuthRequest() error {
	c.logger.Info("[SWuClient] initiating IKE_AUTH request", "message_id", c.transaction.MessageID)

	// Create IKE_AUTH packet with ID payload
	packet := NewGenericPacket(c.transaction, ExchangeTypeIKEAuth)

	// Identification payload
	idPayload := &IdentificationPayload{
		IDType: IdentificationPayloadTypeRFC822,
		NAI:    c.transaction.NAI,
	}
	packet.AddPayload(idPayload)

	// Marshal packet
	data, err := packet.Marshal()
	if err != nil {
		return fmt.Errorf("failed to marshal IKE_AUTH request: %w", err)
	}

	// Register response handler
	retransmitData := RetransmitData{
		RetransmitFunc: c.network.SendNATT,
		PacketData:     data,
	}

	c.responder.WaitResponder(
		c.transaction.MessageID,
		c.handleIKEAuthResponse,
		retransmitData,
		2*time.Second,
	)

	// Send packet via NAT-T
	return c.network.SendNATT(data)
}

// handleIKEAuthResponse handles the IKE_AUTH response with EAP-AKA challenge
func (c *SWuClient) handleIKEAuthResponse(packet *GenericPacket, userdata any) error {
	c.logger.Info("[SWuClient] received IKE_AUTH response")

	// Find EAP payload
	var eapPayload *ExtensibleAuthenticationPayload
	for _, payload := range packet.Payloads {
		if p, ok := payload.(*ExtensibleAuthenticationPayload); ok {
			eapPayload = p
			break
		}
	}

	if eapPayload == nil {
		return fmt.Errorf("no EAP payload in IKE_AUTH response")
	}

	// Parse EAP-AKA request
	eapData := eapPayload.Data
	if len(eapData) < 4 {
		return fmt.Errorf("EAP payload too short")
	}

	eapCode := eapData[0]
	eapID := eapData[1]
	eapLength := binary.BigEndian.Uint16(eapData[2:4])

	if eapCode != 1 { // Request
		return fmt.Errorf("expected EAP request, got code %d", eapCode)
	}

	if len(eapData) < int(eapLength) {
		return fmt.Errorf("EAP data shorter than declared length")
	}

	// Extract EAP-AKA data
	if len(eapData) < 8 {
		return fmt.Errorf("EAP-AKA payload too short")
	}

	eapType := eapData[4]
	if eapType != 23 { // EAP-AKA
		return fmt.Errorf("expected EAP-AKA type 23, got %d", eapType)
	}

	eapSubtype := eapData[5]
	if eapSubtype != 1 { // AKA-Challenge
		return fmt.Errorf("expected AKA-Challenge subtype 1, got %d", eapSubtype)
	}

	// Parse EAP-AKA attributes
	rand, autn, err := c.parseEAPAKAChallenge(eapData[8:])
	if err != nil {
		return fmt.Errorf("failed to parse EAP-AKA challenge: %w", err)
	}

	// Perform USIM authentication
	c.transaction.EAPID = eapID
	authResult, err := c.transaction.PerformUSIMAuth(c.usimCard, rand, autn)
	if err != nil {
		return fmt.Errorf("USIM authentication failed: %w", err)
	}

	// Perform EAP-AKA key derivation
	if err := c.transaction.Crypto.EAP(c.transaction.NAI, authResult.RES, authResult.IK, authResult.CK); err != nil {
		return fmt.Errorf("EAP-AKA key derivation failed: %w", err)
	}

	c.logger.Info("[SWuClient] USIM authentication successful, sending EAP-AKA response")

	// Increment message ID and send EAP-AKA response
	c.transaction.IncrementMessageID()

	return c.sendEAPAKAResponse(authResult)
}

// parseEAPAKAChallenge parses RAND and AUTN from EAP-AKA challenge
func (c *SWuClient) parseEAPAKAChallenge(data []byte) (rand, autn []byte, err error) {
	offset := 0

	for offset < len(data) {
		if offset+4 > len(data) {
			break
		}

		attrType := data[offset]
		attrLength := data[offset+1] * 4 // Length is in 4-byte units

		if offset+int(attrLength) > len(data) {
			break
		}

		switch attrType {
		case 1: // AT_RAND
			if attrLength >= 20 { // 4 bytes header + 2 bytes reserved + 16 bytes RAND
				rand = data[offset+6 : offset+22]
			}
		case 2: // AT_AUTN
			if attrLength >= 20 { // 4 bytes header + 2 bytes reserved + 16 bytes AUTN
				autn = data[offset+6 : offset+22]
			}
		}

		offset += int(attrLength)
	}

	if len(rand) != 16 || len(autn) != 16 {
		return nil, nil, fmt.Errorf("invalid RAND or AUTN length")
	}

	return rand, autn, nil
}

// sendEAPAKAResponse sends the EAP-AKA response
func (c *SWuClient) sendEAPAKAResponse(authResult *AuthResult) error {
	c.logger.Info("[SWuClient] sending EAP-AKA response", "message_id", c.transaction.MessageID)

	// Create EAP-AKA response packet
	eapData := c.createEAPAKAResponse(authResult)

	// Create IKE_AUTH packet
	packet := NewGenericPacket(c.transaction, ExchangeTypeIKEAuth)

	eapPayload := &ExtensibleAuthenticationPayload{
		Data: eapData,
	}
	packet.AddPayload(eapPayload)

	// Marshal packet
	data, err := packet.Marshal()
	if err != nil {
		return fmt.Errorf("failed to marshal EAP-AKA response: %w", err)
	}

	// Register response handler
	retransmitData := RetransmitData{
		RetransmitFunc: c.network.SendNATT,
		PacketData:     data,
	}

	c.responder.WaitResponder(
		c.transaction.MessageID,
		c.handleEAPAKAResult,
		retransmitData,
		2*time.Second,
	)

	// Send packet
	return c.network.SendNATT(data)
}

// createEAPAKAResponse creates an EAP-AKA response packet
func (c *SWuClient) createEAPAKAResponse(_ *AuthResult) []byte {
	// This is a simplified EAP-AKA response
	// In a full implementation, this would include proper AT_RES attribute
	eapData := make([]byte, 8)
	eapData[0] = 2 // Response
	eapData[1] = c.transaction.EAPID
	binary.BigEndian.PutUint16(eapData[2:4], 8) // Length
	eapData[4] = 23                             // EAP-AKA
	eapData[5] = 1                              // AKA-Challenge
	// Reserved bytes 6-7

	// In a full implementation, we would add AT_RES and other attributes here

	return eapData
}

// handleEAPAKAResult handles the EAP success/failure result
func (c *SWuClient) handleEAPAKAResult(packet *GenericPacket, userdata any) error {
	c.logger.Info("[SWuClient] received EAP-AKA result")

	// Find EAP payload
	var eapPayload *ExtensibleAuthenticationPayload
	for _, payload := range packet.Payloads {
		if p, ok := payload.(*ExtensibleAuthenticationPayload); ok {
			eapPayload = p
			break
		}
	}

	if eapPayload == nil {
		return fmt.Errorf("no EAP payload in EAP result")
	}

	eapData := eapPayload.Data
	if len(eapData) < 4 {
		return fmt.Errorf("EAP payload too short")
	}

	eapCode := eapData[0]
	if eapCode != 3 { // Success
		return fmt.Errorf("EAP authentication failed, code: %d", eapCode)
	}

	c.logger.Info("[SWuClient] EAP-AKA authentication successful")

	// Increment message ID and send final IKE_AUTH
	c.transaction.IncrementMessageID()

	return c.sendFinalIKEAuth()
}

// sendFinalIKEAuth sends the final IKE_AUTH request
func (c *SWuClient) sendFinalIKEAuth() error {
	c.logger.Info("[SWuClient] sending final IKE_AUTH", "message_id", c.transaction.MessageID)

	// Create IKE_AUTH packet with AUTH payload
	packet := NewGenericPacket(c.transaction, ExchangeTypeIKEAuth)

	// In a full implementation, we would add AUTH and SA payloads here
	// For now, we'll send a minimal packet

	// Marshal packet
	data, err := packet.Marshal()
	if err != nil {
		return fmt.Errorf("failed to marshal final IKE_AUTH: %w", err)
	}

	// Register response handler
	retransmitData := RetransmitData{
		RetransmitFunc: c.network.SendNATT,
		PacketData:     data,
	}

	c.responder.WaitResponder(
		c.transaction.MessageID,
		c.handleFinalIKEAuthResult,
		retransmitData,
		2*time.Second,
	)

	// Send packet
	return c.network.SendNATT(data)
}

// handleFinalIKEAuthResult handles the final IKE_AUTH response with configuration
func (c *SWuClient) handleFinalIKEAuthResult(packet *GenericPacket, userdata any) error {
	c.logger.Info("[SWuClient] received final IKE_AUTH result")

	// Find configuration payload
	var configPayload *ConfigurationPayload
	for _, payload := range packet.Payloads {
		if p, ok := payload.(*ConfigurationPayload); ok {
			configPayload = p
			break
		}
	}

	if configPayload == nil {
		return fmt.Errorf("no configuration payload in final IKE_AUTH response")
	}

	// Extract IP addresses
	var ipv4, ipv6, pcscf4, pcscf6 string

	for _, attr := range configPayload.Attributes {
		switch attr.Type {
		case 1: // INTERNAL_IP4_ADDRESS
			if len(attr.Value) >= 4 {
				ip := net.IP(attr.Value[:4])
				ipv4 = ip.String()
			}
		case 8: // INTERNAL_IP6_ADDRESS
			if len(attr.Value) >= 16 {
				ip := net.IP(attr.Value[:16])
				ipv6 = ip.String()
			}
		case 20: // P_CSCF_IP4_ADDRESS
			if len(attr.Value) >= 4 {
				ip := net.IP(attr.Value[:4])
				pcscf4 = ip.String()
			}
		case 21: // P_CSCF_IP6_ADDRESS
			if len(attr.Value) >= 16 {
				ip := net.IP(attr.Value[:16])
				pcscf6 = ip.String()
			}
		}
	}

	// Determine which IP version to use
	if ipv6 != "" && pcscf6 != "" {
		c.clientAddr = ipv6
		c.pcscfAddr = pcscf6
	} else if ipv4 != "" && pcscf4 != "" {
		c.clientAddr = ipv4
		c.pcscfAddr = pcscf4
	} else {
		return fmt.Errorf("no matching IP version for client and P-CSCF addresses")
	}

	c.logger.Info("[SWuClient] tunnel configuration received",
		"client_addr", c.clientAddr,
		"pcscf_addr", c.pcscfAddr)

	// Mark handover complete
	c.handover = true

	return nil
}
