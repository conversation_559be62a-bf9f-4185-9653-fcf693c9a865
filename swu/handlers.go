package swu

import (
	"encoding/binary"
	"fmt"
	"net"
	"time"
)

// handleIKESAInitResponse handles the IKE_SA_INIT response
func (c *SWuClient) handleIKESAInitResponse(packet *GenericPacket, userdata any) error {
	c.logger.Info("[SWuClient] received IKE_SA_INIT response")

	// Close NAT connection, switch to NAT-T
	// (In a real implementation, we'd properly manage connection lifecycle)

	// Parse response payloads
	var nonce *NoncePayload
	var sa *SecurityAssociationPayload
	var ke *KeyExchangePayload
	var errorNotify *NotifyPayload

	c.logger.Debug("[SWuClient] parsing IKE_SA_INIT response payloads", "count", len(packet.Payloads))

	for i, payload := range packet.Payloads {
		switch p := payload.(type) {
		case *NoncePayload:
			nonce = p
			c.logger.Debug("[SWuClient] found nonce payload", "index", i, "length", len(p.Data))
		case *SecurityAssociationPayload:
			sa = p
			c.logger.Debug("[SWuClient] found SA payload", "index", i, "proposals", len(p.Proposals))
		case *KeyExchangePayload:
			ke = p
			c.logger.Debug("[SWuClient] found KE payload", "index", i, "dh_group", p.DHGroup, "length", len(p.Data))
		case *NotifyPayload:
			// Check if this is an error notification or informational
			if p.NotifyType == 16388 || p.NotifyType == 16389 {
				// NAT Detection notifications are expected and not errors
				c.logger.Debug("[SWuClient] received NAT detection notification", "index", i, "type", p.NotifyType, "data_len", len(p.Data))
			} else if p.NotifyType < 16384 {
				// Error notifications (0-16383)
				errorNotify = p
				c.logger.Warn("[SWuClient] received error notification", "index", i, "type", p.NotifyType, "data_len", len(p.Data))
			} else {
				// Status notifications (16384+)
				c.logger.Debug("[SWuClient] received status notification", "index", i, "type", p.NotifyType, "data_len", len(p.Data))
			}
		case *GenericPayload:
			c.logger.Warn("[SWuClient] unknown payload type", "index", i, "type", p.PayloadType, "data_len", len(p.Data))
		default:
			c.logger.Warn("[SWuClient] unhandled payload type", "index", i, "type", fmt.Sprintf("%T", payload))
		}
	}

	// Check for error notifications first
	if errorNotify != nil {
		return fmt.Errorf("received error notification in IKE_SA_INIT response: type=%d", errorNotify.NotifyType)
	}

	if nonce == nil || sa == nil || ke == nil {
		missing := []string{}
		if nonce == nil {
			missing = append(missing, "nonce")
		}
		if sa == nil {
			missing = append(missing, "SA")
		}
		if ke == nil {
			missing = append(missing, "KE")
		}
		return fmt.Errorf("missing required payloads in IKE_SA_INIT response: %v", missing)
	}

	// Validate DH group
	if ke.DHGroup != c.transaction.IKEDH {
		return fmt.Errorf("DH group mismatch: expected %d, got %d", c.transaction.IKEDH, ke.DHGroup)
	}

	// Extract crypto parameters from selected proposal
	if len(sa.Proposals) == 0 {
		return fmt.Errorf("no proposals in SA payload")
	}

	proposal := sa.Proposals[0]
	var encr, encrLength, integ, prf uint16

	c.logger.Debug("[SWuClient] parsing selected proposal", "transforms", len(proposal.Transforms))

	for i, transform := range proposal.Transforms {
		c.logger.Debug("[SWuClient] processing transform", "index", i, "type", transform.TransformType, "id", transform.TransformID, "attrs", len(transform.Attributes))

		switch transform.TransformType {
		case TransformTypeEncr:
			encr = transform.TransformID
			// Extract key length from attributes
			for j, attr := range transform.Attributes {
				c.logger.Debug("[SWuClient] processing encr attribute", "index", j, "type", attr.Type, "value_len", len(attr.Value))
				if attr.Type == 14 || attr.Type == 0x800E { // Key Length attribute (AF=0 or AF=1)
					if attr.Type&0x8000 != 0 {
						// AF=1 format - value is in the length field
						if len(attr.Value) >= 2 {
							encrLength = binary.BigEndian.Uint16(attr.Value)
						}
					} else {
						// AF=0 format - value is in the data
						if len(attr.Value) >= 2 {
							encrLength = binary.BigEndian.Uint16(attr.Value)
						}
					}
					c.logger.Debug("[SWuClient] found key length", "length", encrLength)
				}
			}
		case TransformTypeInteg:
			integ = transform.TransformID
		case TransformTypePRF:
			prf = transform.TransformID
		}
	}

	c.logger.Debug("[SWuClient] extracted crypto parameters", "encr", encr, "encr_length", encrLength, "integ", integ, "prf", prf)

	if encr == 0 || integ == 0 || prf == 0 {
		return fmt.Errorf("incomplete crypto parameters: encr=%d, integ=%d, prf=%d", encr, integ, prf)
	}

	// For AES-CBC, if no key length is specified, default to 128
	if encr == EncrAESCBC && encrLength == 0 {
		encrLength = 128
		c.logger.Debug("[SWuClient] defaulting AES-CBC key length to 128")
	}

	// Set crypto parameters
	c.transaction.SetCryptoMode(encr, encrLength, integ, prf)
	c.transaction.SPIResponder = packet.SPIResponder
	c.transaction.SetCryptoSPI()
	c.transaction.NonceResponder = nonce.Data
	c.transaction.SetCryptoNonce()

	// Generate shared secret
	if err := c.transaction.Crypto.GenerateIKESecret(ke.Data); err != nil {
		return fmt.Errorf("failed to generate IKE secret: %w", err)
	}

	// Derive keys
	if err := c.transaction.Crypto.DeriveKeys(); err != nil {
		return fmt.Errorf("failed to derive keys: %w", err)
	}

	c.logger.Info("[SWuClient] IKE_SA_INIT completed, starting IKE_AUTH")

	// Increment message ID and start IKE_AUTH
	c.transaction.IncrementMessageID()

	return c.initiateIKEAuthRequest()
}

// initiateIKEAuthRequest starts the IKE_AUTH exchange with EAP-AKA
func (c *SWuClient) initiateIKEAuthRequest() error {
	c.logger.Info("[SWuClient] initiating IKE_AUTH request", "message_id", c.transaction.MessageID)

	// Create IKE_AUTH packet with ID payload
	packet := NewGenericPacket(c.transaction, ExchangeTypeIKEAuth)

	// Identification payload
	idPayload := &IdentificationPayload{
		IDType: IdentificationPayloadTypeRFC822,
		NAI:    c.transaction.NAI,
	}
	packet.AddPayload(idPayload)

	// Marshal packet
	data, err := packet.Marshal()
	if err != nil {
		return fmt.Errorf("failed to marshal IKE_AUTH request: %w", err)
	}

	// Register response handler
	retransmitData := RetransmitData{
		RetransmitFunc: c.network.SendNATT,
		PacketData:     data,
	}

	c.responder.WaitResponder(
		c.transaction.MessageID,
		c.handleIKEAuthResponse,
		retransmitData,
		2*time.Second,
	)

	// Send packet via NAT-T
	return c.network.SendNATT(data)
}

// handleIKEAuthResponse handles the IKE_AUTH response with EAP-AKA challenge
func (c *SWuClient) handleIKEAuthResponse(packet *GenericPacket, userdata any) error {
	c.logger.Info("[SWuClient] received IKE_AUTH response")

	// Get the raw packet data for integrity verification
	rawPacketData, ok := userdata.([]byte)
	if !ok {
		return fmt.Errorf("userdata is not raw packet data")
	}

	c.logger.Debug("[SWuClient] raw packet data for integrity check", "len", len(rawPacketData), "first_bytes", fmt.Sprintf("%x", rawPacketData[:min(32, len(rawPacketData))]))
	c.logger.Debug("[SWuClient] packet header info", "spi_i", fmt.Sprintf("%x", packet.SPIInitiator), "spi_r", fmt.Sprintf("%x", packet.SPIResponder), "msg_id", packet.MessageID)

	// Check IKE message flags to determine direction
	if len(rawPacketData) >= 20 {
		flags := rawPacketData[19]
		isResponse := (flags & 0x20) != 0  // Response flag is bit 5
		isInitiator := (flags & 0x08) != 0 // Initiator flag is bit 3
		c.logger.Debug("[SWuClient] IKE message flags", "flags", fmt.Sprintf("0x%02x", flags), "is_response", isResponse, "is_initiator", isInitiator)
	}

	// Debug: log all payloads in the response
	c.logger.Debug("[SWuClient] parsing IKE_AUTH response payloads", "count", len(packet.Payloads))
	for i, payload := range packet.Payloads {
		switch p := payload.(type) {
		case *ExtensibleAuthenticationPayload:
			c.logger.Debug("[SWuClient] found EAP payload", "index", i, "data_len", len(p.Data))
		case *EncryptedAndAuthenticatedPayload:
			c.logger.Debug("[SWuClient] found encrypted payload", "index", i, "data_len", len(p.Data))
		case *NotifyPayload:
			c.logger.Debug("[SWuClient] found notify payload", "index", i, "type", p.NotifyType, "data_len", len(p.Data))
		case *IdentificationPayload:
			c.logger.Debug("[SWuClient] found ID payload", "index", i, "type", p.IDType, "nai_len", len(p.NAI))
		case *GenericPayload:
			c.logger.Debug("[SWuClient] found generic payload", "index", i, "type", p.PayloadType, "data_len", len(p.Data))
		default:
			c.logger.Debug("[SWuClient] found unknown payload", "index", i, "type", fmt.Sprintf("%T", payload))
		}
	}

	// Find EAP payload (might be encrypted)
	var eapPayload *ExtensibleAuthenticationPayload

	// First check for direct EAP payload
	for _, payload := range packet.Payloads {
		if p, ok := payload.(*ExtensibleAuthenticationPayload); ok {
			eapPayload = p
			break
		}
	}

	// If no direct EAP payload, check for encrypted payload
	if eapPayload == nil {
		for i, payload := range packet.Payloads {
			if encPayload, ok := payload.(*EncryptedAndAuthenticatedPayload); ok {
				c.logger.Debug("[SWuClient] decrypting encrypted payload")
				decryptedData, err := c.transaction.Crypto.DecryptPayload(encPayload.Data, rawPacketData)
				if err != nil {
					return fmt.Errorf("failed to decrypt payload: %w", err)
				}

				// Parse the decrypted data as payloads
				// We need to manually parse the payload chain from decrypted data
				if len(decryptedData) < 4 {
					return fmt.Errorf("decrypted data too short")
				}

				// Parse the decrypted data as a payload chain
				c.logger.Debug("[SWuClient] parsing decrypted payload chain", "data_len", len(decryptedData))
				maxLen := min(64, len(decryptedData))
				c.logger.Debug("[SWuClient] decrypted data hex", "data", fmt.Sprintf("%x", decryptedData[:maxLen]))

				// Get the next payload type from the encrypted payload
				// We need to find what payload type should be inside the encrypted payload
				// Look at the packet structure to find the next payload type for this encrypted payload
				var nextPayloadType uint8 = PayloadTypeExtensibleAuthentication // Default assumption

				// Try to find the next payload type from the packet parsing context
				// The encrypted payload should have been parsed with a next payload type
				// For now, let's examine the raw packet data to find the encrypted payload header
				c.logger.Debug("[SWuClient] attempting to find next payload type for encrypted payload", "payload_index", i)

				payloads, err := c.parsePayloadChain(decryptedData, nextPayloadType)
				if err != nil {
					c.logger.Debug("[SWuClient] failed to parse decrypted payload chain", "error", err)

					// Manual parsing - try to find EAP payload in the data
					// Look for EAP payload type (48 = 0x30) in the data
					for i := 0; i < len(decryptedData)-4; i++ {
						if decryptedData[i] == PayloadTypeExtensibleAuthentication {
							c.logger.Debug("[SWuClient] found potential EAP payload at offset", "offset", i)
							// Check if this looks like a valid payload header
							if i+4 <= len(decryptedData) {
								payloadLength := binary.BigEndian.Uint16(decryptedData[i+2 : i+4])
								if i+int(payloadLength) <= len(decryptedData) && payloadLength >= 4 {
									eapPayload = &ExtensibleAuthenticationPayload{
										Data: decryptedData[i+4 : i+int(payloadLength)],
									}
									c.logger.Debug("[SWuClient] extracted EAP payload", "data_len", len(eapPayload.Data))
									break
								}
							}
						}
					}
				} else {
					// Successfully parsed payload chain, look for EAP or Configuration payload
					c.logger.Debug("[SWuClient] successfully parsed decrypted payload chain", "payload_count", len(payloads))
					for _, payload := range payloads {
						switch p := payload.(type) {
						case *ExtensibleAuthenticationPayload:
							eapPayload = p
							c.logger.Debug("[SWuClient] found EAP payload in decrypted chain", "data_len", len(p.Data))
						case *ConfigurationPayload:
							c.logger.Debug("[SWuClient] found Configuration payload in decrypted chain",
								"config_type", p.ConfigType,
								"attributes_count", len(p.Attributes))
							// Handle configuration payload
							// This might contain IP configuration or other settings
						default:
							c.logger.Debug("[SWuClient] found other payload in decrypted chain", "type", fmt.Sprintf("%T", p))
						}
					}
				}
				break
			}
		}
	}

	if eapPayload == nil {
		return fmt.Errorf("no EAP payload in IKE_AUTH response")
	}

	// Parse EAP-AKA request
	eapData := eapPayload.Data
	if len(eapData) < 4 {
		return fmt.Errorf("EAP payload too short")
	}

	eapCode := eapData[0]
	eapID := eapData[1]
	eapLength := binary.BigEndian.Uint16(eapData[2:4])

	c.logger.Debug("[SWuClient] EAP message details",
		"code", eapCode,
		"id", eapID,
		"length", eapLength,
		"data_len", len(eapData),
		"raw_data", fmt.Sprintf("%x", eapData[:minInt(32, len(eapData))]))

	switch eapCode {
	case 1: // Request
		c.logger.Debug("[SWuClient] received EAP Request")
	case 2: // Response
		c.logger.Debug("[SWuClient] received EAP Response")
		return fmt.Errorf("unexpected EAP response, got code %d", eapCode)
	case 3: // Success
		c.logger.Info("[SWuClient] received EAP Success!")
		// EAP authentication completed successfully
		// TODO: Handle EAP success and proceed to next phase
		return nil
	case 4: // Failure
		c.logger.Error("[SWuClient] received EAP Failure!")
		return fmt.Errorf("EAP authentication failed")
	default:
		return fmt.Errorf("unknown EAP code %d", eapCode)
	}

	if eapCode != 1 { // Only process requests
		return fmt.Errorf("expected EAP request, got code %d", eapCode)
	}

	if len(eapData) < int(eapLength) {
		return fmt.Errorf("EAP data shorter than declared length")
	}

	// Extract EAP-AKA data
	if len(eapData) < 8 {
		return fmt.Errorf("EAP-AKA payload too short")
	}

	eapType := eapData[4]
	if eapType != 23 { // EAP-AKA
		return fmt.Errorf("expected EAP-AKA type 23, got %d", eapType)
	}

	eapSubtype := eapData[5]
	if eapSubtype != 1 { // AKA-Challenge
		return fmt.Errorf("expected AKA-Challenge subtype 1, got %d", eapSubtype)
	}

	// Parse EAP-AKA attributes
	rand, autn, err := c.parseEAPAKAChallenge(eapData[8:])
	if err != nil {
		return fmt.Errorf("failed to parse EAP-AKA challenge: %w", err)
	}

	// Perform USIM authentication
	c.transaction.EAPID = eapID
	authResult, err := c.transaction.PerformUSIMAuth(c.usimCard, rand, autn)
	if err != nil {
		return fmt.Errorf("USIM authentication failed: %w", err)
	}

	// Perform EAP-AKA key derivation
	if err := c.transaction.Crypto.EAP(c.transaction.NAI, authResult.RES, authResult.IK, authResult.CK); err != nil {
		return fmt.Errorf("EAP-AKA key derivation failed: %w", err)
	}

	c.logger.Info("[SWuClient] USIM authentication successful, sending EAP-AKA response")

	// Increment message ID and send EAP-AKA response
	c.transaction.IncrementMessageID()

	return c.sendEAPAKAResponse(authResult)
}

// parseEAPAKAChallenge parses RAND and AUTN from EAP-AKA challenge
func (c *SWuClient) parseEAPAKAChallenge(data []byte) (rand, autn []byte, err error) {
	c.logger.Debug("[SWuClient] parsing EAP-AKA challenge", "data_len", len(data), "raw_data", fmt.Sprintf("%x", data))

	offset := 0

	for offset < len(data) {
		if offset+4 > len(data) {
			break
		}

		attrType := data[offset]
		attrLength := data[offset+1] * 4 // Length is in 4-byte units

		c.logger.Debug("[SWuClient] EAP-AKA attribute", "type", attrType, "length", attrLength, "offset", offset)

		if offset+int(attrLength) > len(data) {
			break
		}

		switch attrType {
		case 1: // AT_RAND
			if attrLength >= 20 { // 4 bytes header + 2 bytes reserved + 16 bytes RAND
				rawValue := data[offset+2 : offset+int(attrLength)]
				// Extract 16 bytes starting from offset+4 (skip type, length, then 2 bytes reserved)
				rand = data[offset+4 : offset+4+16]
				fmt.Printf("[DEBUG_EXTRACT] Go AT_RAND raw_value: %x\n", rawValue)
				fmt.Printf("[DEBUG_EXTRACT] Go AT_RAND extracted: %x\n", rand)
				c.logger.Debug("[SWuClient] extracted AT_RAND", "rand", fmt.Sprintf("%x", rand))
			}
		case 2: // AT_AUTN
			if attrLength >= 20 { // 4 bytes header + 2 bytes reserved + 16 bytes AUTN
				rawValue := data[offset+2 : offset+int(attrLength)]
				// Extract 16 bytes starting from offset+4 (skip type, length, then 2 bytes reserved)
				autn = data[offset+4 : offset+4+16]
				fmt.Printf("[DEBUG_EXTRACT] Go AT_AUTN raw_value: %x\n", rawValue)
				fmt.Printf("[DEBUG_EXTRACT] Go AT_AUTN extracted: %x\n", autn)
				c.logger.Debug("[SWuClient] extracted AT_AUTN", "autn", fmt.Sprintf("%x", autn))
			}
		}

		offset += int(attrLength)
	}

	if len(rand) != 16 || len(autn) != 16 {
		return nil, nil, fmt.Errorf("invalid RAND or AUTN length")
	}

	return rand, autn, nil
}

// sendEAPAKAResponse sends the EAP-AKA response
func (c *SWuClient) sendEAPAKAResponse(authResult *AuthResult) error {
	c.logger.Info("[SWuClient] sending EAP-AKA response", "message_id", c.transaction.MessageID)

	// Create EAP-AKA response packet
	eapData := c.createEAPAKAResponse(authResult)

	// Create IKE_AUTH packet
	packet := NewGenericPacket(c.transaction, ExchangeTypeIKEAuth)

	eapPayload := &ExtensibleAuthenticationPayload{
		Data: eapData,
	}
	packet.AddPayload(eapPayload)

	// Marshal packet
	data, err := packet.Marshal()
	if err != nil {
		return fmt.Errorf("failed to marshal EAP-AKA response: %w", err)
	}

	// Register response handler
	retransmitData := RetransmitData{
		RetransmitFunc: c.network.SendNATT,
		PacketData:     data,
	}

	c.responder.WaitResponder(
		c.transaction.MessageID,
		c.handleEAPAKAResult,
		retransmitData,
		2*time.Second,
	)

	// Send packet
	return c.network.SendNATT(data)
}

// createEAPAKAResponse creates an EAP-AKA response packet
func (c *SWuClient) createEAPAKAResponse(authResult *AuthResult) []byte {
	// Calculate total length: 8 bytes header + AT_RES attribute
	resAttrLen := 4 + 2 + len(authResult.RES) // 4 bytes header + 2 bytes length + RES data
	// Pad to 4-byte boundary
	if resAttrLen%4 != 0 {
		resAttrLen += 4 - (resAttrLen % 4)
	}

	totalLen := 8 + resAttrLen
	eapData := make([]byte, totalLen)

	// EAP header
	eapData[0] = 2 // Response
	eapData[1] = c.transaction.EAPID
	binary.BigEndian.PutUint16(eapData[2:4], uint16(totalLen)) // Length
	eapData[4] = 23                                            // EAP-AKA
	eapData[5] = 1                                             // AKA-Challenge
	// Reserved bytes 6-7

	// AT_RES attribute
	offset := 8
	eapData[offset] = 3                                                                   // AT_RES
	eapData[offset+1] = uint8(resAttrLen / 4)                                             // Length in 4-byte units
	binary.BigEndian.PutUint16(eapData[offset+2:offset+4], uint16(len(authResult.RES)*8)) // RES length in bits
	copy(eapData[offset+4:], authResult.RES)                                              // RES data

	return eapData
}

// handleEAPAKAResult handles the EAP success/failure result
func (c *SWuClient) handleEAPAKAResult(packet *GenericPacket, userdata any) error {
	c.logger.Info("[SWuClient] received EAP-AKA result")

	// Get the raw packet data for integrity verification
	rawPacketData, ok := userdata.([]byte)
	if !ok {
		return fmt.Errorf("userdata is not raw packet data")
	}

	// Debug: log all payload types
	c.logger.Debug("[SWuClient] EAP result payload analysis", "payload_count", len(packet.Payloads))
	for i, payload := range packet.Payloads {
		c.logger.Debug("[SWuClient] payload type analysis", "index", i, "type", fmt.Sprintf("%T", payload))
	}

	// Find EAP payload
	var eapPayload *ExtensibleAuthenticationPayload
	for _, payload := range packet.Payloads {
		if p, ok := payload.(*ExtensibleAuthenticationPayload); ok {
			eapPayload = p
			break
		}
	}

	if eapPayload == nil {
		// Check if this is an encrypted payload that needs decryption
		for _, payload := range packet.Payloads {
			if encPayload, ok := payload.(*EncryptedAndAuthenticatedPayload); ok {
				c.logger.Debug("[SWuClient] found encrypted payload in EAP result, attempting decryption")
				return c.handleEncryptedEAPResult(encPayload, rawPacketData)
			}
		}
		return fmt.Errorf("no EAP payload in EAP result")
	}

	eapData := eapPayload.Data
	if len(eapData) < 4 {
		return fmt.Errorf("EAP payload too short")
	}

	eapCode := eapData[0]
	if eapCode != 3 { // Success
		return fmt.Errorf("EAP authentication failed, code: %d", eapCode)
	}

	c.logger.Info("[SWuClient] EAP-AKA authentication successful")

	// Increment message ID and send final IKE_AUTH
	c.transaction.IncrementMessageID()

	return c.sendFinalIKEAuth()
}

// sendFinalIKEAuth sends the final IKE_AUTH request with ONLY Authentication payload
// This matches PHP's IKEAuthRequest.php which only sends Authentication payload
func (c *SWuClient) sendFinalIKEAuth() error {
	c.logger.Info("[SWuClient] sending final IKE_AUTH with Authentication payload only", "message_id", c.transaction.MessageID)

	// Create IKE_AUTH packet - PHP IKEAuthRequest.php only sends Authentication payload
	packet := NewGenericPacket(c.transaction, ExchangeTypeIKEAuth)

	// ONLY Authentication payload - matching PHP IKEAuthRequest.php implementation
	// PHP IKEAuthRequest.php only sends Authentication payload, not all the other payloads
	// Calculate authentication hash using initiator ID payload raw data (not including payload header)
	// PHP uses getRawPayload() which returns only the data part, not the full payload
	idRawData := make([]byte, 4+len(c.transaction.NAI)) // type(1) + reserved(3) + data
	idRawData[0] = 3                                    // TYPE_ID_RFC822_ADDR
	// bytes 1-3 are reserved (zero)
	copy(idRawData[4:], []byte(c.transaction.NAI))

	authHash, err := c.transaction.Crypto.CalculateAuthenticationHash(
		idRawData,
		c.transaction.IKESAInitPacketBuffer,
		c.transaction.NonceResponder,
	)
	if err != nil {
		return fmt.Errorf("failed to calculate authentication hash: %w", err)
	}

	authPayload := &AuthenticationPayload{
		Method: 2, // METHOD_SHARED_KEY_MESSAGE_INTEGRITY_CODE
		Data:   authHash,
	}
	packet.AddPayload(authPayload)

	c.logger.Info("[SWuClient] final IKE_AUTH with ONLY Authentication payload (matching PHP IKEAuthRequest.php)")

	// Debug: Marshal the packet to see the final structure
	if packetData, err := packet.Marshal(); err == nil {
		c.logger.Info("[SWuClient] final IKE_AUTH packet structure",
			"total_size", len(packetData),
			"hex_dump", fmt.Sprintf("%x", packetData))
	}

	// Marshal packet
	data, err := packet.Marshal()
	if err != nil {
		return fmt.Errorf("failed to marshal final IKE_AUTH: %w", err)
	}

	// Register response handler
	retransmitData := RetransmitData{
		RetransmitFunc: c.network.SendNATT,
		PacketData:     data,
	}

	c.responder.WaitResponder(
		c.transaction.MessageID,
		c.handleFinalIKEAuthResult,
		retransmitData,
		2*time.Second,
	)

	// Also register handler for CREATE_CHILD_SA exchange (message ID 0) in case configuration comes there
	c.responder.WaitResponder(
		0, // CREATE_CHILD_SA uses message ID 0
		c.handleCreateChildSA,
		nil,
		10*time.Second, // Longer timeout for CREATE_CHILD_SA
	)

	// Send packet
	return c.network.SendNATT(data)
}

// handleFinalIKEAuthResult handles the final IKE_AUTH response with configuration
func (c *SWuClient) handleFinalIKEAuthResult(packet *GenericPacket, userdata any) error {
	c.logger.Info("[SWuClient] ========== FINAL IKE_AUTH PHASE ==========")
	c.logger.Info("[SWuClient] received final IKE_AUTH result", "payload_count", len(packet.Payloads))

	// Get the raw packet data for integrity verification
	rawPacketData, ok := userdata.([]byte)
	if !ok {
		return fmt.Errorf("userdata is not raw packet data")
	}

	c.logger.Info("[SWuClient] raw final IKE_AUTH packet data", "len", len(rawPacketData), "hex", fmt.Sprintf("%x", rawPacketData))

	// Log all payloads in the message
	for i, payload := range packet.Payloads {
		c.logger.Info("[SWuClient] final IKE_AUTH payload", "index", i, "type", fmt.Sprintf("%T", payload))
	}

	// Check if this is an encrypted payload that needs decryption
	for _, payload := range packet.Payloads {
		if encPayload, ok := payload.(*EncryptedAndAuthenticatedPayload); ok {
			c.logger.Debug("[SWuClient] found encrypted payload in final IKE_AUTH result, attempting decryption")
			return c.handleEncryptedFinalIKEAuth(encPayload, rawPacketData)
		}
	}

	// Find configuration payload
	var configPayload *ConfigurationPayload
	for _, payload := range packet.Payloads {
		if p, ok := payload.(*ConfigurationPayload); ok {
			configPayload = p
			break
		}
	}

	if configPayload == nil {
		return fmt.Errorf("no configuration payload in final IKE_AUTH response")
	}

	// Extract IP addresses
	var ipv4, ipv6, pcscf4, pcscf6 string

	for _, attr := range configPayload.Attributes {
		switch attr.Type {
		case 1: // INTERNAL_IP4_ADDRESS
			if len(attr.Value) >= 4 {
				ip := net.IP(attr.Value[:4])
				ipv4 = ip.String()
			}
		case 8: // INTERNAL_IP6_ADDRESS
			if len(attr.Value) >= 16 {
				ip := net.IP(attr.Value[:16])
				ipv6 = ip.String()
			}
		case 20: // P_CSCF_IP4_ADDRESS
			if len(attr.Value) >= 4 {
				ip := net.IP(attr.Value[:4])
				pcscf4 = ip.String()
			}
		case 21: // P_CSCF_IP6_ADDRESS
			if len(attr.Value) >= 16 {
				ip := net.IP(attr.Value[:16])
				pcscf6 = ip.String()
			}
		}
	}

	// Determine which IP version to use
	if ipv6 != "" && pcscf6 != "" {
		c.clientAddr = ipv6
		c.pcscfAddr = pcscf6
	} else if ipv4 != "" && pcscf4 != "" {
		c.clientAddr = ipv4
		c.pcscfAddr = pcscf4
	} else {
		return fmt.Errorf("no matching IP version for client and P-CSCF addresses")
	}

	c.logger.Info("[SWuClient] tunnel configuration received",
		"client_addr", c.clientAddr,
		"pcscf_addr", c.pcscfAddr)

	// Mark handover complete
	c.handover = true

	return nil
}

// minInt returns the minimum of two integers
func minInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// parsePayloadChain parses a chain of payloads from decrypted data
// This is similar to GenericPayload::parseChain in the PHP implementation
func (c *SWuClient) parsePayloadChain(data []byte, initialPayloadType uint8) ([]Payload, error) {
	var payloads []Payload
	offset := 0

	// The decrypted data doesn't include payload type headers, just the payload data
	if len(data) < 4 {
		return nil, fmt.Errorf("payload chain data too short")
	}

	nextPayloadType := initialPayloadType
	c.logger.Debug("[SWuClient] starting payload chain parsing", "first_payload_type", nextPayloadType)

	currentPayloadType := initialPayloadType

	for currentPayloadType != PayloadTypeNone && offset < len(data) {
		if offset+4 > len(data) {
			return nil, fmt.Errorf("insufficient data for payload header at offset %d", offset)
		}

		// Read payload header
		nextPayloadType = data[offset]
		flags := data[offset+1]
		payloadLength := binary.BigEndian.Uint16(data[offset+2 : offset+4])

		c.logger.Debug("[SWuClient] parsing payload", "current_type", currentPayloadType, "next_type", nextPayloadType, "length", payloadLength, "offset", offset, "flags", flags)

		if payloadLength < 4 {
			return nil, fmt.Errorf("invalid payload length %d at offset %d", payloadLength, offset)
		}

		if offset+int(payloadLength) > len(data) {
			return nil, fmt.Errorf("payload extends beyond data boundary at offset %d", offset)
		}

		// Extract payload data (excluding header)
		payloadData := data[offset+4 : offset+int(payloadLength)]

		// Create payload based on current type (not the type in the header)
		var payload Payload
		switch currentPayloadType {
		case PayloadTypeExtensibleAuthentication:
			payload = &ExtensibleAuthenticationPayload{}
		case PayloadTypeIdentificationInitiator, PayloadTypeIdentificationResponder:
			payload = &IdentificationPayload{}
		case PayloadTypeNotify:
			payload = &NotifyPayload{}
		case PayloadTypeSecurityAssociation:
			payload = &SecurityAssociationPayload{}
		case PayloadTypeKeyExchange:
			payload = &KeyExchangePayload{}
		case PayloadTypeNonce:
			payload = &NoncePayload{}
		case PayloadTypeConfiguration:
			payload = &ConfigurationPayload{}
		default:
			// Unknown payload type, create generic payload
			payload = &GenericPayload{PayloadType: currentPayloadType}
		}

		// Unmarshal payload data
		if err := payload.Unmarshal(payloadData); err != nil {
			c.logger.Warn("[SWuClient] failed to unmarshal payload", "type", currentPayloadType, "error", err)
			// Create generic payload as fallback
			payload = &GenericPayload{
				PayloadType: currentPayloadType,
				Data:        payloadData,
			}
		}

		payloads = append(payloads, payload)

		// Move to next payload
		offset += int(payloadLength)
		currentPayloadType = nextPayloadType

		// If this was an encrypted payload, stop parsing
		if currentPayloadType == PayloadTypeEncryptedAndAuthenticated {
			break
		}
	}

	c.logger.Debug("[SWuClient] payload chain parsing complete", "payload_count", len(payloads))
	return payloads, nil
}

// handleEncryptedEAPResult handles encrypted EAP result payloads
func (c *SWuClient) handleEncryptedEAPResult(encPayload *EncryptedAndAuthenticatedPayload, rawPacketData []byte) error {
	c.logger.Debug("[SWuClient] decrypting EAP result payload")

	// Decrypt the payload using the full packet data for integrity check
	decryptedData, err := c.transaction.Crypto.DecryptPayload(encPayload.Data, rawPacketData)
	if err != nil {
		return fmt.Errorf("failed to decrypt EAP result payload: %w", err)
	}

	c.logger.Debug("[SWuClient] EAP result payload decrypted", "decrypted_len", len(decryptedData))

	// Parse the decrypted payload chain
	// For EAP result, we expect EAP payload type
	payloads, err := c.parsePayloadChain(decryptedData, PayloadTypeExtensibleAuthentication)
	if err != nil {
		return fmt.Errorf("failed to parse decrypted EAP result payload chain: %w", err)
	}

	// Look for EAP payload in decrypted data
	var eapPayload *ExtensibleAuthenticationPayload
	for _, payload := range payloads {
		if p, ok := payload.(*ExtensibleAuthenticationPayload); ok {
			eapPayload = p
			break
		}
	}

	if eapPayload == nil {
		c.logger.Debug("[SWuClient] no EAP payload found in decrypted result, checking for other payloads")
		// Log all payload types found
		for i, payload := range payloads {
			c.logger.Debug("[SWuClient] decrypted payload type", "index", i, "type", fmt.Sprintf("%T", payload))
		}

		// This might be the final IKE_AUTH completion - check for generic payloads
		// In IKEv2, the final authentication might not contain EAP but other payload types
		c.logger.Info("[SWuClient] EAP Success received - authentication complete")

		// Increment message ID and send final IKE_AUTH
		c.transaction.IncrementMessageID()
		return c.sendFinalIKEAuth()
	}

	// Process the EAP payload
	eapData := eapPayload.Data
	if len(eapData) < 4 {
		return fmt.Errorf("EAP payload too short")
	}

	eapCode := eapData[0]
	eapID := eapData[1]
	eapLength := binary.BigEndian.Uint16(eapData[2:4])

	c.logger.Debug("[SWuClient] decrypted EAP message details",
		"code", eapCode,
		"id", eapID,
		"length", eapLength,
		"data_len", len(eapData),
		"raw_data", fmt.Sprintf("%x", eapData[:minInt(16, len(eapData))]))

	switch eapCode {
	case 1: // Request - might be EAP-AKA Notification
		if len(eapData) >= 6 {
			eapType := eapData[4]
			if eapType == 23 { // EAP-AKA
				eapSubtype := eapData[5]
				c.logger.Debug("[SWuClient] EAP-AKA message", "subtype", eapSubtype)
				if eapSubtype == 12 { // Notification
					c.logger.Info("[SWuClient] received EAP-AKA Notification - authentication successful")
					// Increment message ID and send final IKE_AUTH
					c.transaction.IncrementMessageID()
					return c.sendFinalIKEAuth()
				}
			}
		}
		return fmt.Errorf("unexpected EAP request in result, code: %d", eapCode)
	case 3: // Success
		c.logger.Info("[SWuClient] EAP-AKA authentication successful")
		// Increment message ID and send final IKE_AUTH
		c.transaction.IncrementMessageID()
		return c.sendFinalIKEAuth()
	default:
		return fmt.Errorf("EAP authentication failed, code: %d", eapCode)
	}
}

// handleEncryptedFinalIKEAuth handles encrypted final IKE_AUTH response
func (c *SWuClient) handleEncryptedFinalIKEAuth(encPayload *EncryptedAndAuthenticatedPayload, rawPacketData []byte) error {
	c.logger.Info("[SWuClient] ========== DECRYPTING FINAL IKE_AUTH ==========")
	c.logger.Info("[SWuClient] decrypting final IKE_AUTH payload", "encrypted_len", len(encPayload.Data))
	c.logger.Info("[SWuClient] encrypted data", "hex", fmt.Sprintf("%x", encPayload.Data))

	// Decrypt the payload
	decryptedData, err := c.transaction.Crypto.DecryptPayload(encPayload.Data, rawPacketData)
	if err != nil {
		return fmt.Errorf("failed to decrypt final IKE_AUTH payload: %w", err)
	}

	c.logger.Info("[SWuClient] final IKE_AUTH payload decrypted successfully", "decrypted_len", len(decryptedData))
	c.logger.Info("[SWuClient] decrypted data", "hex", fmt.Sprintf("%x", decryptedData))

	// Parse the decrypted payload chain - we need to determine the first payload type from the decrypted data
	c.logger.Debug("[SWuClient] analyzing decrypted final IKE_AUTH data", "len", len(decryptedData), "hex", fmt.Sprintf("%x", decryptedData))

	if len(decryptedData) < 4 {
		c.logger.Info("[SWuClient] decrypted final IKE_AUTH data too short - likely empty response or notification")
		return nil
	}

	// Parse the 8-byte response as potential error/status information
	if len(decryptedData) == 8 {
		length := binary.BigEndian.Uint32(decryptedData[0:4])
		statusCode := binary.BigEndian.Uint32(decryptedData[4:8])
		c.logger.Info("[SWuClient] final IKE_AUTH 8-byte response analysis",
			"length", length,
			"status_code", statusCode,
			"status_hex", fmt.Sprintf("0x%x", statusCode))

		// Status code 24 (0x18) might indicate some specific condition
		if statusCode == 0x18 {
			c.logger.Info("[SWuClient] received status code 0x18 - may indicate configuration will come via CREATE_CHILD_SA")
		}
		return nil
	}

	// Check if this looks like a valid payload chain
	// The first byte should be a valid payload type (0 means no next payload)
	firstPayloadType := decryptedData[0]
	if firstPayloadType == 0 {
		c.logger.Info("[SWuClient] final IKE_AUTH response contains no payloads (first byte is 0)")
		return nil
	}

	payloads, err := c.parsePayloadChain(decryptedData, firstPayloadType)
	if err != nil {
		c.logger.Warn("[SWuClient] failed to parse decrypted final IKE_AUTH payload chain", "error", err)
		// This might be a notification or other non-standard response
		c.logger.Info("[SWuClient] final IKE_AUTH response may contain notification or non-standard data")
		return nil
	}

	c.logger.Debug("[SWuClient] successfully parsed decrypted final IKE_AUTH payload chain", "payload_count", len(payloads))

	// Check what payloads we have
	c.logger.Debug("[SWuClient] analyzing decrypted final IKE_AUTH payloads", "payload_count", len(payloads))
	for i, payload := range payloads {
		c.logger.Debug("[SWuClient] payload analysis", "index", i, "type", fmt.Sprintf("%T", payload))
	}

	// Find configuration payload
	var configPayload *ConfigurationPayload
	for _, payload := range payloads {
		if p, ok := payload.(*ConfigurationPayload); ok {
			configPayload = p
			break
		}
	}

	// If no configuration payload, this might be a notification or empty response
	// indicating that configuration will come in CREATE_CHILD_SA exchange
	if configPayload == nil {
		c.logger.Info("[SWuClient] no configuration payload in final IKE_AUTH response - configuration may come in CREATE_CHILD_SA exchange")
		return nil
	}

	// Extract IP addresses
	var ipv4, ipv6, pcscf4, pcscf6 string

	for _, attr := range configPayload.Attributes {
		switch attr.Type {
		case 1: // INTERNAL_IP4_ADDRESS
			if len(attr.Value) >= 4 {
				ip := net.IP(attr.Value[:4])
				ipv4 = ip.String()
			}
		case 8: // INTERNAL_IP6_ADDRESS
			if len(attr.Value) >= 16 {
				ip := net.IP(attr.Value[:16])
				ipv6 = ip.String()
			}
		case 20: // P_CSCF_IP4_ADDRESS
			if len(attr.Value) >= 4 {
				ip := net.IP(attr.Value[:4])
				pcscf4 = ip.String()
			}
		case 21: // P_CSCF_IP6_ADDRESS
			if len(attr.Value) >= 16 {
				ip := net.IP(attr.Value[:16])
				pcscf6 = ip.String()
			}
		}
	}

	c.logger.Info("[SWuClient] SWu IKEv2 tunnel established successfully",
		"ipv4", ipv4,
		"ipv6", ipv6,
		"pcscf4", pcscf4,
		"pcscf6", pcscf6)

	// TODO: Set up tunnel interface and routing
	// TODO: Start IMS registration

	return nil
}

// handleCreateChildSA handles CREATE_CHILD_SA exchange for IPsec tunnel establishment
func (c *SWuClient) handleCreateChildSA(packet *GenericPacket, userdata any) error {
	c.logger.Info("[SWuClient] ========== CREATE_CHILD_SA PHASE ==========")
	c.logger.Info("[SWuClient] received CREATE_CHILD_SA request", "payload_count", len(packet.Payloads))

	// Get the raw packet data for integrity verification
	rawPacketData, ok := userdata.([]byte)
	if !ok {
		return fmt.Errorf("userdata is not raw packet data")
	}

	c.logger.Info("[SWuClient] raw CREATE_CHILD_SA packet data", "len", len(rawPacketData), "hex", fmt.Sprintf("%x", rawPacketData))

	// Log all payloads in the message
	for i, payload := range packet.Payloads {
		c.logger.Info("[SWuClient] CREATE_CHILD_SA payload", "index", i, "type", fmt.Sprintf("%T", payload))
	}

	// Check if this is an encrypted payload that needs decryption
	for _, payload := range packet.Payloads {
		if encPayload, ok := payload.(*EncryptedAndAuthenticatedPayload); ok {
			c.logger.Debug("[SWuClient] found encrypted payload in CREATE_CHILD_SA, attempting decryption")
			return c.handleEncryptedCreateChildSA(encPayload, rawPacketData)
		}
	}

	c.logger.Debug("[SWuClient] CREATE_CHILD_SA payload analysis", "payload_count", len(packet.Payloads))
	for i, payload := range packet.Payloads {
		c.logger.Debug("[SWuClient] CREATE_CHILD_SA payload", "index", i, "type", fmt.Sprintf("%T", payload))
	}

	// TODO: Handle unencrypted CREATE_CHILD_SA payloads if needed
	c.logger.Info("[SWuClient] CREATE_CHILD_SA exchange received - tunnel establishment in progress")

	return nil
}

// handleEncryptedCreateChildSA handles encrypted CREATE_CHILD_SA payload
func (c *SWuClient) handleEncryptedCreateChildSA(encPayload *EncryptedAndAuthenticatedPayload, rawPacketData []byte) error {
	c.logger.Info("[SWuClient] ========== DECRYPTING CREATE_CHILD_SA ==========")
	c.logger.Info("[SWuClient] decrypting CREATE_CHILD_SA payload", "encrypted_len", len(encPayload.Data))
	c.logger.Info("[SWuClient] encrypted data", "hex", fmt.Sprintf("%x", encPayload.Data))

	// Decrypt the payload
	decryptedData, err := c.transaction.Crypto.DecryptPayload(encPayload.Data, rawPacketData)
	if err != nil {
		return fmt.Errorf("failed to decrypt CREATE_CHILD_SA payload: %w", err)
	}

	c.logger.Info("[SWuClient] CREATE_CHILD_SA payload decrypted successfully", "decrypted_len", len(decryptedData))
	c.logger.Info("[SWuClient] decrypted data", "hex", fmt.Sprintf("%x", decryptedData))

	// Parse the decrypted payload chain
	c.logger.Debug("[SWuClient] analyzing decrypted CREATE_CHILD_SA data", "len", len(decryptedData), "hex", fmt.Sprintf("%x", decryptedData))

	if len(decryptedData) < 4 {
		c.logger.Info("[SWuClient] decrypted CREATE_CHILD_SA data too short - likely empty response or notification")
		return nil
	}

	// Parse the 8-byte response as potential error/status information
	if len(decryptedData) == 8 {
		length := binary.BigEndian.Uint32(decryptedData[0:4])
		statusCode := binary.BigEndian.Uint32(decryptedData[4:8])
		c.logger.Info("[SWuClient] CREATE_CHILD_SA 8-byte response analysis",
			"length", length,
			"status_code", statusCode,
			"status_hex", fmt.Sprintf("0x%x", statusCode))

		// Status code 1 might indicate some specific condition
		if statusCode == 0x01000000 {
			c.logger.Info("[SWuClient] received status code 0x01000000 - may indicate successful tunnel establishment")
			// Proceed with tunnel establishment using configuration from IKE_AUTH
			return c.establishIPSecTunnel()
		}
		return nil
	}

	// Check if this looks like a valid payload chain
	firstPayloadType := decryptedData[0]
	if firstPayloadType == 0 {
		c.logger.Info("[SWuClient] CREATE_CHILD_SA response contains no payloads (first byte is 0)")
		return nil
	}

	payloads, err := c.parsePayloadChain(decryptedData, firstPayloadType)
	if err != nil {
		c.logger.Warn("[SWuClient] failed to parse decrypted CREATE_CHILD_SA payload chain", "error", err)
		// This might be a notification or other non-standard response
		c.logger.Info("[SWuClient] CREATE_CHILD_SA response may contain notification or non-standard data")
		return nil
	}

	c.logger.Debug("[SWuClient] successfully parsed decrypted CREATE_CHILD_SA payload chain", "payload_count", len(payloads))

	// Look for configuration payload in CREATE_CHILD_SA
	var configPayload *ConfigurationPayload
	for _, payload := range payloads {
		if p, ok := payload.(*ConfigurationPayload); ok {
			configPayload = p
			break
		}
	}

	if configPayload != nil {
		c.logger.Info("[SWuClient] found configuration payload in CREATE_CHILD_SA")
		return c.processConfigurationPayload(configPayload)
	}

	// Analyze what payloads we have
	c.logger.Debug("[SWuClient] analyzing CREATE_CHILD_SA payloads", "payload_count", len(payloads))
	for i, payload := range payloads {
		c.logger.Debug("[SWuClient] CREATE_CHILD_SA payload", "index", i, "type", fmt.Sprintf("%T", payload))
	}

	c.logger.Info("[SWuClient] CREATE_CHILD_SA exchange processed - tunnel establishment in progress")
	return nil
}

// processConfigurationPayload extracts IP configuration from configuration payload
func (c *SWuClient) processConfigurationPayload(configPayload *ConfigurationPayload) error {
	c.logger.Info("[SWuClient] ========== PROCESSING CONFIGURATION PAYLOAD ==========")
	c.logger.Info("[SWuClient] configuration payload details", "config_type", configPayload.ConfigType, "attribute_count", len(configPayload.Attributes))

	// Log all attributes
	for i, attr := range configPayload.Attributes {
		c.logger.Info("[SWuClient] configuration attribute", "index", i, "type", attr.Type, "length", attr.Length, "value_hex", fmt.Sprintf("%x", attr.Value))
	}

	// Extract IP addresses
	var ipv4, ipv6, pcscf4, pcscf6 string

	for _, attr := range configPayload.Attributes {
		switch attr.Type {
		case 1: // INTERNAL_IP4_ADDRESS
			if len(attr.Value) >= 4 {
				ip := net.IP(attr.Value[:4])
				ipv4 = ip.String()
			}
		case 8: // INTERNAL_IP6_ADDRESS
			if len(attr.Value) >= 16 {
				ip := net.IP(attr.Value[:16])
				ipv6 = ip.String()
			}
		case 20: // P_CSCF_IP4_ADDRESS
			if len(attr.Value) >= 4 {
				ip := net.IP(attr.Value[:4])
				pcscf4 = ip.String()
			}
		case 21: // P_CSCF_IP6_ADDRESS
			if len(attr.Value) >= 16 {
				ip := net.IP(attr.Value[:16])
				pcscf6 = ip.String()
			}
		}
	}

	c.logger.Info("[SWuClient] SWu IKEv2 tunnel established successfully",
		"ipv4", ipv4,
		"ipv6", ipv6,
		"pcscf4", pcscf4,
		"pcscf6", pcscf6)

	// Store addresses for later use
	c.clientAddr = ipv4
	if pcscf4 != "" {
		c.pcscfAddr = pcscf4
	} else if pcscf6 != "" {
		c.pcscfAddr = pcscf6
	}

	// Set handover flag to indicate successful completion
	c.handover = true

	c.logger.Info("[SWuClient] ========== CONFIGURATION COMPLETE ==========")
	c.logger.Info("[SWuClient] VoWiFi tunnel configuration successful - ready for IMS registration")

	// Establish IPSec tunnel
	return c.establishIPSecTunnel()
}
