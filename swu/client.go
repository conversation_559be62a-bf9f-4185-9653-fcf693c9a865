package swu

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"time"

	"github.com/damonto/vowifi/usim"
)

// SWuClient implements the SWu interface IKEv2 client
type SWuClient struct {
	ctx    context.Context
	logger *slog.Logger

	// Configuration
	IMEI        string
	ePDGAddress string
	mark        int

	// Components
	usimCard    *usim.USIM
	network     *Network
	transaction *Transaction
	responder   *ResponderManager

	// State
	handover   bool
	clientAddr string
	pcscfAddr  string
}

// NewSWuClient creates a new SWu client
func NewSWuClient(ctx context.Context, imei string, usimCard *usim.USIM, epdgAddress string, mark int, logger *slog.Logger) (*SWuClient, error) {
	client := &SWuClient{
		ctx:         ctx,
		logger:      logger,
		IMEI:        imei,
		ePDGAddress: epdgAddress,
		mark:        mark,
		usimCard:    usimCard,
		handover:    false,
	}

	// Get IMSI from USIM
	imsi, err := usimCard.GetIMSI()
	if err != nil {
		return nil, fmt.Errorf("failed to get IMSI: %w", err)
	}

	// Construct NAI and ePDG address if not provided
	nai := fmt.Sprintf("<EMAIL>%s.mcc%s.3gppnetwork.org", imsi.String(), imsi.MNC(), imsi.MCC())

	if client.ePDGAddress == "" {
		client.ePDGAddress = fmt.Sprintf("epdg.epc.mnc%s.mcc%s.pub.3gppnetwork.org", imsi.MNC(), imsi.MCC())
		logger.Info("[SWuClient] using default ePDG address", "address", client.ePDGAddress)
	} else {
		logger.Info("[SWuClient] using provided ePDG address", "address", client.ePDGAddress)
	}

	// Create transaction
	client.transaction, err = NewTransaction(imei, nai, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// Create network
	client.network, err = NewNetwork(ctx, client.ePDGAddress, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create network: %w", err)
	}

	// Create responder manager
	client.responder = NewResponderManager(client.network, logger)

	logger.Info("[SWuClient] created",
		"imei", imei,
		"imsi", imsi.String(),
		"nai", nai,
		"epdg_address", client.ePDGAddress)

	return client, nil
}

// Run starts the SWu connection process
func (c *SWuClient) Run() error {
	c.logger.Info("[SWuClient] initializing SWu connection...")

	// Establish network connections
	if err := c.network.Dial(); err != nil {
		return fmt.Errorf("failed to dial ePDG: %w", err)
	}

	// Start packet listener
	c.network.ListenPacket(c.handlePacket)

	// Set transaction network addresses
	if nattAddr := c.network.GetNATTLocalAddr(); nattAddr != nil {
		if udpAddr, ok := nattAddr.(*net.UDPAddr); ok {
			c.transaction.AddrC = udpAddr.IP.String()
			c.transaction.PortC = uint16(udpAddr.Port)
		}
	}

	// Start IKE_SA_INIT exchange
	if err := c.initiateIKESAInit(); err != nil {
		return fmt.Errorf("failed to initiate IKE_SA_INIT: %w", err)
	}

	// Main event loop
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for !c.handover {
		select {
		case <-c.ctx.Done():
			return c.ctx.Err()
		case <-ticker.C:
			if err := c.responder.CheckTimeouts(); err != nil {
				return fmt.Errorf("timeout error: %w", err)
			}
		}
	}

	c.logger.Info("[SWuClient] SWu connection established successfully",
		"client_addr", c.clientAddr,
		"pcscf_addr", c.pcscfAddr)

	return nil
}

// handlePacket processes incoming packets
func (c *SWuClient) handlePacket(from NetworkType, data []byte) error {
	c.logger.Debug("[SWuClient] received packet", "from", from, "size", len(data))

	var packetData []byte
	switch from {
	case NetworkTypeNAT:
		packetData = data
	case NetworkTypeNATT:
		// Remove Non-ESP marker (first 4 bytes)
		if len(data) < 4 {
			c.logger.Warn("[SWuClient] NAT-T packet too short")
			return nil
		}
		marker := data[:4]
		if marker[0] == 0 && marker[1] == 0 && marker[2] == 0 && marker[3] == 0 {
			packetData = data[4:]
		} else {
			c.logger.Warn("[SWuClient] unhandled ESP packet")
			return nil
		}
	default:
		return fmt.Errorf("unknown network type: %v", from)
	}

	// Parse packet
	packet, err := UnmarshalGenericPacket(packetData, c.logger)
	if err != nil {
		c.logger.Error("[SWuClient] failed to parse packet", "error", err)
		return nil
	}

	// Process with responder manager
	return c.responder.ProcessResponderPacket(packet)
}

// initiateIKESAInit starts the IKE_SA_INIT exchange
func (c *SWuClient) initiateIKESAInit() error {
	c.logger.Info("[SWuClient] initiating IKE_SA_INIT", "message_id", c.transaction.MessageID)

	// Create IKE_SA_INIT packet
	packet := c.createIKESAInitPacket()

	// Marshal packet
	data, err := packet.Marshal()
	if err != nil {
		return fmt.Errorf("failed to marshal IKE_SA_INIT packet: %w", err)
	}

	// Store packet for retransmission
	c.transaction.IKESAInitPacketBuffer = data

	// Register response handler
	retransmitData := RetransmitData{
		RetransmitFunc: c.network.SendNAT,
		PacketData:     data,
	}

	c.responder.WaitResponder(
		c.transaction.MessageID,
		c.handleIKESAInitResponse,
		retransmitData,
		2*time.Second,
	)

	// Send packet
	if err := c.network.SendNAT(data); err != nil {
		return fmt.Errorf("failed to send IKE_SA_INIT packet: %w", err)
	}

	// Switch to NAT-T port for subsequent communications
	c.transaction.PortS = 4500

	return nil
}

// createIKESAInitPacket creates an IKE_SA_INIT packet
func (c *SWuClient) createIKESAInitPacket() *GenericPacket {
	packet := NewGenericPacket(c.transaction, ExchangeTypeIKESAInit)

	// Security Association payload
	saPayload := c.createSecurityAssociationPayload()
	packet.AddPayload(saPayload)
	c.logger.Debug("[SWuClient] added SA payload")

	// Key Exchange payload
	kePayload := &KeyExchangePayload{
		DHGroup: c.transaction.IKEDH,
		Data:    c.transaction.Crypto.GetDHPublicKey(),
	}
	packet.AddPayload(kePayload)
	c.logger.Debug("[SWuClient] added KE payload", "dh_group", c.transaction.IKEDH, "key_len", len(c.transaction.Crypto.GetDHPublicKey()))

	// Nonce payload
	noncePayload := &NoncePayload{
		Data: c.transaction.NonceInitiator,
	}
	packet.AddPayload(noncePayload)
	c.logger.Debug("[SWuClient] added nonce payload", "nonce_len", len(c.transaction.NonceInitiator))

	// NAT Detection payloads (force NAT-T)
	natSrcPayload := &NotifyPayload{
		ProtocolID: 0,
		SPISize:    0,
		NotifyType: 16388,            // NAT_DETECTION_SOURCE_IP
		Data:       make([]byte, 20), // Force NAT-T with zeros
	}
	packet.AddPayload(natSrcPayload)
	c.logger.Debug("[SWuClient] added NAT source payload")

	natDstPayload := &NotifyPayload{
		ProtocolID: 0,
		SPISize:    0,
		NotifyType: 16389,            // NAT_DETECTION_DESTINATION_IP
		Data:       make([]byte, 20), // Placeholder
	}
	packet.AddPayload(natDstPayload)
	c.logger.Debug("[SWuClient] added NAT dest payload")

	return packet
}

// createSecurityAssociationPayload creates a security association payload for IKE_SA_INIT
func (c *SWuClient) createSecurityAssociationPayload() *SecurityAssociationPayload {
	// Create a single proposal matching PHP implementation exactly
	// PHP uses: ENCR=12 (AES-CBC-128), INTEG=1 (HMAC-MD5-96), PRF=1 (HMAC-MD5), DH_GROUP=2
	proposals := []Proposal{
		{
			ProposalNumber: 1,
			ProtocolID:     1, // IKE
			SPISize:        0,
			Transforms: []Transform{
				{TransformType: TransformTypeEncr, TransformID: EncrAESCBC, Attributes: []TransformAttribute{{Type: 0x800E, Value: []byte{0, 128}}}}, // AES-CBC-128 with AF=1
				{TransformType: TransformTypeInteg, TransformID: AuthHMACMD596},                                                                      // HMAC-MD5-96
				{TransformType: TransformTypePRF, TransformID: PRFHMACMD5},                                                                           // HMAC-MD5
				{TransformType: TransformTypeDH, TransformID: DHGroup1024MODP},                                                                       // DH Group 2
			},
		},
	}

	c.logger.Debug("[SWuClient] created SA proposal",
		"encr_id", EncrAESCBC,
		"integ_id", AuthHMACMD596,
		"prf_id", PRFHMACMD5,
		"dh_id", DHGroup1024MODP)

	return &SecurityAssociationPayload{
		Proposals: proposals,
	}
}

// GetClientAddress returns the assigned client IP address
func (c *SWuClient) GetClientAddress() string {
	return c.clientAddr
}

// GetPCSCFAddress returns the P-CSCF IP address
func (c *SWuClient) GetPCSCFAddress() string {
	return c.pcscfAddr
}

// Close closes the SWu client and cleans up resources
func (c *SWuClient) Close() error {
	if c.network != nil {
		return c.network.Close()
	}
	return nil
}
