package swu

import (
	"context"
	"log/slog"
	"os"
	"testing"
	"time"

	"github.com/damonto/vowifi/usim"
)

func TestSWuClientCreation(t *testing.T) {
	// Create a test logger
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))

	// Create a mock USIM - skip this test for now since we need a proper USIM mock
	t.Skip("Skipping test that requires USIM mock implementation")

	// Test client creation
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var usimCard *usim.USIM
	client, err := NewSWuClient(ctx, "123456789012345", usimCard, "test.epdg.example.com", 0, logger)
	if err != nil {
		t.Fatalf("Failed to create SWu client: %v", err)
	}

	if client == nil {
		t.Fatal("Client is nil")
	}

	// Test that client has required components
	if client.transaction == nil {
		t.Error("Transaction is nil")
	}

	if client.network == nil {
		t.Error("Network is nil")
	}

	if client.responder == nil {
		t.Error("Responder manager is nil")
	}

	// Test client cleanup
	if err := client.Close(); err != nil {
		t.Errorf("Failed to close client: %v", err)
	}
}

func TestGenericPacketMarshalUnmarshal(t *testing.T) {
	// Create a test logger
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))

	// Create a test transaction
	transaction, err := NewTransaction("123456789012345", "<EMAIL>", logger)
	if err != nil {
		t.Fatalf("Failed to create transaction: %v", err)
	}

	// Create a test packet
	packet := NewGenericPacket(transaction, ExchangeTypeIKESAInit)

	// Add a nonce payload
	noncePayload := &NoncePayload{
		Data: []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16},
	}
	packet.AddPayload(noncePayload)

	// Marshal the packet
	data, err := packet.Marshal()
	if err != nil {
		t.Fatalf("Failed to marshal packet: %v", err)
	}

	if len(data) < 28 {
		t.Errorf("Packet too short: %d bytes", len(data))
	}

	// Unmarshal the packet
	unmarshaled, err := UnmarshalGenericPacket(data, logger)
	if err != nil {
		t.Fatalf("Failed to unmarshal packet: %v", err)
	}

	// Verify packet fields
	if unmarshaled.SPIInitiator != packet.SPIInitiator {
		t.Errorf("SPI initiator mismatch: expected %x, got %x", packet.SPIInitiator, unmarshaled.SPIInitiator)
	}

	if unmarshaled.ExchangeType != packet.ExchangeType {
		t.Errorf("Exchange type mismatch: expected %d, got %d", packet.ExchangeType, unmarshaled.ExchangeType)
	}

	if len(unmarshaled.Payloads) != 1 {
		t.Errorf("Expected 1 payload, got %d", len(unmarshaled.Payloads))
	}

	// Verify nonce payload
	if nonce, ok := unmarshaled.Payloads[0].(*NoncePayload); ok {
		if len(nonce.Data) != 16 {
			t.Errorf("Nonce length mismatch: expected 16, got %d", len(nonce.Data))
		}
	} else {
		t.Error("First payload is not a nonce payload")
	}
}

func TestCryptoHelperDH(t *testing.T) {
	// Create a test logger
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))

	// Create crypto helper
	crypto := NewCryptoHelper(logger)

	// Test DH key generation
	err := crypto.SetCryptoDH(DHGroup1024MODP)
	if err != nil {
		t.Fatalf("Failed to set DH group: %v", err)
	}

	// Get public key
	pubKey := crypto.GetDHPublicKey()
	if len(pubKey) != 128 {
		t.Errorf("DH public key length mismatch: expected 128, got %d", len(pubKey))
	}

	// Test shared secret generation with self (for testing)
	err = crypto.GenerateIKESecret(pubKey)
	if err != nil {
		t.Fatalf("Failed to generate IKE secret: %v", err)
	}

	// Test key derivation setup
	crypto.SetCryptoMode(PRFHMACSHA1, AuthHMACSHA196, EncrAESCBC, 128)
	crypto.SetSPI(0x1234567890abcdef, 0xfedcba0987654321)
	crypto.SetNonce(
		[]byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16},
		[]byte{16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1},
	)

	// Test key derivation
	err = crypto.DeriveKeys()
	if err != nil {
		t.Fatalf("Failed to derive keys: %v", err)
	}
}

func TestSecurityAssociationPayload(t *testing.T) {
	// Create a simple test SA payload
	saPayload := &SecurityAssociationPayload{
		Proposals: []Proposal{
			{
				ProposalNumber: 1,
				ProtocolID:     1,
				SPISize:        0,
				Transforms: []Transform{
					{
						TransformType: TransformTypeEncr,
						TransformID:   EncrAESCBC,
					},
				},
			},
		},
	}

	// Test marshal
	data, err := saPayload.Marshal()
	if err != nil {
		t.Fatalf("Failed to marshal SA payload: %v", err)
	}

	if len(data) == 0 {
		t.Error("SA payload data is empty")
	}

	// For now, just test that marshaling works
	// The SA payload marshaling/unmarshaling is complex and would need more work
	// to handle all the edge cases properly
	t.Logf("SA payload marshaled to %d bytes", len(data))
}

func TestIKEv2Integration(t *testing.T) {
	// Create a test logger
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))

	// Skip this test for now since we need a proper USIM mock
	t.Skip("Skipping test that requires USIM mock implementation")

	// Create IKEv2 instance
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	var usimCard *usim.USIM
	imei := usim.IMEI("123456789012345")
	ikev2, err := NewIKEv2(ctx, usimCard, imei, "test.epdg.example.com", logger)
	if err != nil {
		t.Fatalf("Failed to create IKEv2: %v", err)
	}

	if ikev2 == nil {
		t.Fatal("IKEv2 is nil")
	}

	// Test that client is created
	if ikev2.client == nil {
		t.Error("IKEv2 client is nil")
	}

	// Test address getters (should return empty strings initially)
	clientAddr := ikev2.GetClientAddress()
	if clientAddr != "" {
		t.Errorf("Expected empty client address, got %s", clientAddr)
	}

	pcscfAddr := ikev2.GetPCSCFAddress()
	if pcscfAddr != "" {
		t.Errorf("Expected empty P-CSCF address, got %s", pcscfAddr)
	}

	// Test cleanup
	if err := ikev2.Close(); err != nil {
		t.Errorf("Failed to close IKEv2: %v", err)
	}
}
