[2025-06-28 8:02:04](IMSClient): [SUCC] IMSClient master
[2025-06-28 8:02:04](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 8:02:04](PCSC<PERSON><PERSON><PERSON><PERSON>ider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 8:02:04](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 8:02:04](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 8:02:04](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 8:02:04](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 8:02:04](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 8:02:04](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 8:02:05](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 8:02:05](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 8:02:05](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 8:02:05](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 8:02:05](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 8:02:06](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 8:02:06](SWuClient): [SUCC] SWu connection established
[2025-06-28 8:02:06](IMSClient): [INFO] P-CSCF Addr: fd00:976a:14ef:50::5
[2025-06-28 8:02:06](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:ddb1:a697:ac39:909b:6576:7a4a]
[2025-06-28 8:02:06](IMSSocketPool): [INFO] Self Port Initial: 22076, Client: 22737, Server: 43768
[2025-06-28 8:02:06](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 8:02:06](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:14ef:50::5]:5060
[2025-06-28 8:02:06](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 8:02:06](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 8:02:06](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:ddb1:a697:ac39:909b:6576:7a4a]:43768
[2025-06-28 8:02:07](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:14ef:50::5]:65529
[2025-06-28 8:02:07](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 8:02:07](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 8:02:07](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 8:02:07](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 8:02:08](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 8:02:08](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 8:02:09](IMSClient): [SUCC] Bye
[2025-06-28 9:46:05](IMSClient): [SUCC] IMSClient master
[2025-06-28 9:46:05](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 9:46:05](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 9:46:05](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 9:46:05](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 9:46:05](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 9:46:05](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 9:46:05](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 9:46:05](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 9:46:06](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 9:46:06](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 9:46:06](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 9:46:06](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 9:46:06](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 9:46:07](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 9:46:07](SWuClient): [SUCC] SWu connection established
[2025-06-28 9:46:07](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:13d::5
[2025-06-28 9:46:07](IMSClient): [SUCC] Bye
[2025-06-28 9:49:44](IMSClient): [SUCC] IMSClient master
[2025-06-28 9:49:44](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 9:49:44](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 9:49:45](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 9:49:45](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 9:49:45](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 9:49:45](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 9:49:45](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 9:49:45](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 9:49:45](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 9:49:45](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 9:49:46](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 9:49:46](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 9:49:46](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 9:49:47](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 9:49:47](SWuClient): [SUCC] SWu connection established
[2025-06-28 9:49:47](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:131::5
[2025-06-28 9:49:47](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:d398:39bd:ac39:8feb:4d5b:b14c]
[2025-06-28 9:49:47](IMSSocketPool): [INFO] Self Port Initial: 52672, Client: 37787, Server: 52294
[2025-06-28 9:49:47](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 9:49:47](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:131::5]:5060
[2025-06-28 9:49:47](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 9:49:47](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 9:49:47](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:d398:39bd:ac39:8feb:4d5b:b14c]:52294
[2025-06-28 9:49:48](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:131::5]:65529
[2025-06-28 9:49:48](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 9:49:48](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 9:49:48](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 9:49:48](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 9:49:48](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 9:49:48](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 9:50:04](IMSClient): [SUCC] Bye
[2025-06-28 9:59:29](IMSClient): [SUCC] IMSClient master
[2025-06-28 9:59:29](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 9:59:29](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 9:59:30](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 9:59:30](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 9:59:30](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 9:59:30](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 9:59:30](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 9:59:30](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 9:59:30](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 9:59:30](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 9:59:31](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 9:59:31](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 9:59:31](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 9:59:31](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 9:59:31](SWuClient): [SUCC] SWu connection established
[2025-06-28 9:59:31](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:15b::5
[2025-06-28 9:59:31](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:d390:6c30:ac39:8feb:4b7b:be1b]
[2025-06-28 9:59:31](IMSSocketPool): [INFO] Self Port Initial: 27405, Client: 47711, Server: 53113
[2025-06-28 9:59:31](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 9:59:32](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:15b::5]:5060
[2025-06-28 9:59:32](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 9:59:32](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 9:59:32](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:d390:6c30:ac39:8feb:4b7b:be1b]:53113
[2025-06-28 9:59:32](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:15b::5]:65529
[2025-06-28 9:59:32](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 9:59:33](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 9:59:33](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 9:59:33](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 9:59:33](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 9:59:33](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 9:59:51](IMSClient): [SUCC] Bye
[2025-06-28 10:05:35](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:05:35](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:05:35](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:05:35](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:05:35](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:05:35](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:05:35](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:05:36](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:05:36](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 10:05:36](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 10:05:36](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 10:05:36](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 10:05:36](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 10:05:36](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 10:05:37](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 10:05:37](SWuClient): [SUCC] SWu connection established
[2025-06-28 10:05:37](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:143::5
[2025-06-28 10:05:37](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:cff2:a4b:ac39:8fd3:4bd4:12e0]
[2025-06-28 10:05:37](IMSSocketPool): [INFO] Self Port Initial: 16649, Client: 14019, Server: 29529
[2025-06-28 10:05:37](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 10:05:37](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:143::5]:5060
[2025-06-28 10:05:37](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 10:05:38](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 10:05:38](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:cff2:a4b:ac39:8fd3:4bd4:12e0]:29529
[2025-06-28 10:05:38](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:143::5]:65529
[2025-06-28 10:05:38](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 10:05:38](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 10:05:38](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 10:05:38](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 10:05:39](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 10:05:39](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 10:05:56](IMSClient): [SUCC] Bye
[2025-06-28 10:22:48](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:22:48](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:22:48](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:22:49](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:22:49](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:22:49](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:22:49](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:22:49](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:22:49](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 10:22:49](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 10:22:49](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 10:22:50](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 10:22:50](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 10:22:50](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 10:22:50](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 10:22:50](SWuClient): [SUCC] SWu connection established
[2025-06-28 10:22:50](IMSClient): [INFO] P-CSCF Addr: fd00:976a:c207:25::5
[2025-06-28 10:22:50](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:5e5d:1e2e:ac39:52ba:6463:9a89]
[2025-06-28 10:22:50](IMSSocketPool): [INFO] Self Port Initial: 53207, Client: 11303, Server: 58187
[2025-06-28 10:22:50](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 10:22:51](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:c207:25::5]:5060
[2025-06-28 10:22:51](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 10:22:52](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 10:22:52](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:5e5d:1e2e:ac39:52ba:6463:9a89]:58187
[2025-06-28 10:22:52](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:c207:25::5]:65529
[2025-06-28 10:22:52](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 10:22:52](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 10:22:52](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 10:22:52](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 10:22:53](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 10:22:53](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 10:23:09](IMSClient): [SUCC] Bye
[2025-06-28 10:28:46](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:28:46](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:28:46](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:28:46](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:28:46](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:28:46](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:28:46](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:28:46](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:28:46](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 10:28:47](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 10:28:47](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 10:28:47](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 10:28:47](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 10:28:47](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 10:28:48](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 10:28:48](SWuClient): [SUCC] SWu connection established
[2025-06-28 10:28:48](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:115::5
[2025-06-28 10:28:48](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:147:8e1f:ac39:883a:6543:9041]
[2025-06-28 10:28:48](IMSSocketPool): [INFO] Self Port Initial: 35645, Client: 40239, Server: 63632
[2025-06-28 10:28:48](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 10:28:48](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:115::5]:5060
[2025-06-28 10:28:48](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 10:28:48](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 10:28:49](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:147:8e1f:ac39:883a:6543:9041]:63632
[2025-06-28 10:28:49](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:115::5]:65529
[2025-06-28 10:28:49](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 10:28:49](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 10:28:49](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 10:28:49](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 10:28:50](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 10:28:50](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 10:29:14](IMSClient): [SUCC] Bye
[2025-06-28 10:37:32](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:37:32](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:37:32](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:37:32](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:37:33](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:37:33](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:37:33](IMSClient): [SUCC] Bye
[2025-06-28 10:44:16](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:44:16](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:44:16](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:44:16](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:44:16](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:44:16](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:44:16](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:44:16](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:44:16](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 10:44:17](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 10:44:17](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 10:44:17](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 10:44:17](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 10:44:17](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 10:44:18](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 10:44:18](SWuClient): [SUCC] SWu connection established
[2025-06-28 10:44:18](IMSClient): [INFO] P-CSCF Addr: fd00:976a:c202:1808::5
[2025-06-28 10:44:18](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:9b9e:2e0c:ac39:8a33:7d7f:87e0]
[2025-06-28 10:44:18](IMSSocketPool): [INFO] Self Port Initial: 62608, Client: 57932, Server: 63499
[2025-06-28 10:44:18](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 10:44:18](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:c202:1808::5]:5060
[2025-06-28 10:44:18](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 10:44:19](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 10:44:19](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:9b9e:2e0c:ac39:8a33:7d7f:87e0]:63499
[2025-06-28 10:44:19](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:c202:1808::5]:65529
[2025-06-28 10:44:19](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 10:44:19](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 10:44:19](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 10:44:19](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 10:44:20](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 10:44:20](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 10:47:08](IMSClient): [SUCC] Bye
[2025-06-28 10:48:18](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:48:18](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:48:18](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:48:19](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:48:19](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:48:19](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:48:19](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:48:19](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:48:19](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 10:48:19](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 10:48:19](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 10:48:20](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 10:48:20](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 10:48:20](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 10:48:20](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 10:48:20](SWuClient): [SUCC] SWu connection established
[2025-06-28 10:48:20](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:23d::5
[2025-06-28 10:48:20](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:f249:91a5:ac39:90da:64c7:38ce]
[2025-06-28 10:48:20](IMSSocketPool): [INFO] Self Port Initial: 61850, Client: 11762, Server: 15822
[2025-06-28 10:48:20](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 10:48:21](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:23d::5]:5060
[2025-06-28 10:48:21](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 10:48:21](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 10:48:21](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:f249:91a5:ac39:90da:64c7:38ce]:15822
[2025-06-28 10:48:22](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:23d::5]:65529
[2025-06-28 10:48:22](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 10:48:22](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 10:48:22](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 10:48:22](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 10:48:23](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 10:48:23](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 10:48:24](IMSClient): [SUCC] Bye
[2025-06-28 10:49:12](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:49:12](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:49:12](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:49:12](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:49:12](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:49:12](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:49:12](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:49:13](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:49:13](IMSClient): [SUCC] Bye
[2025-06-28 10:55:00](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:55:00](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:55:00](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:55:01](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:55:01](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:55:01](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:55:01](IMSClient): [SUCC] Bye
[2025-06-28 10:55:59](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:55:59](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:55:59](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:55:59](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:55:59](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:55:59](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:55:59](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:55:59](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:55:59](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 10:56:00](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 10:56:00](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 10:56:00](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 10:56:00](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 10:56:00](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 10:56:01](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 10:56:01](SWuClient): [SUCC] SWu connection established
[2025-06-28 10:56:01](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:149::5
[2025-06-28 10:56:01](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:a20a:85a6:ac39:52da:7f44:d0e7]
[2025-06-28 10:56:01](IMSSocketPool): [INFO] Self Port Initial: 36906, Client: 25792, Server: 14440
[2025-06-28 10:56:01](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 10:56:01](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:149::5]:5060
[2025-06-28 10:56:01](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 10:56:02](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 10:56:02](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:a20a:85a6:ac39:52da:7f44:d0e7]:14440
[2025-06-28 10:56:02](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:149::5]:65529
[2025-06-28 10:56:02](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 10:56:02](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 10:56:02](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 10:56:02](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 10:56:03](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 10:56:03](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 10:58:06](IMSClient): [SUCC] Bye
[2025-06-28 10:58:14](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:58:14](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:58:14](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:58:14](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:58:14](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:58:14](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:58:14](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:58:15](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:58:15](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 10:58:15](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 10:58:15](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 10:58:16](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 10:58:16](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 10:58:16](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 10:58:16](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 10:58:16](SWuClient): [SUCC] SWu connection established
[2025-06-28 10:58:16](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:145::5
[2025-06-28 10:58:16](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:6dd8:426:ac39:befb:647d:bc1]
[2025-06-28 10:58:16](IMSSocketPool): [INFO] Self Port Initial: 14286, Client: 62018, Server: 60341
[2025-06-28 10:58:16](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 10:58:17](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:145::5]:5060
[2025-06-28 10:58:17](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 10:58:17](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 10:58:17](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:6dd8:426:ac39:befb:647d:bc1]:60341
[2025-06-28 10:58:17](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:145::5]:65529
[2025-06-28 10:58:17](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 10:58:18](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 10:58:18](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 10:58:18](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 10:58:18](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 10:58:18](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 10:59:09](IMSClient): [SUCC] Bye
[2025-06-28 10:59:29](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:59:29](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:59:29](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:59:29](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:59:29](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:59:29](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:59:29](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:59:29](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:59:29](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 10:59:30](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 10:59:30](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 10:59:30](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 10:59:30](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 10:59:30](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 10:59:31](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 10:59:31](SWuClient): [SUCC] SWu connection established
[2025-06-28 10:59:31](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:242::5
[2025-06-28 10:59:31](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:336f:bcbd:ac39:906a:4c4c:5197]
[2025-06-28 10:59:31](IMSSocketPool): [INFO] Self Port Initial: 12039, Client: 63166, Server: 44417
[2025-06-28 10:59:31](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 10:59:31](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:242::5]:5060
[2025-06-28 10:59:31](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 10:59:32](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 10:59:32](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:336f:bcbd:ac39:906a:4c4c:5197]:44417
[2025-06-28 10:59:32](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:242::5]:65529
[2025-06-28 10:59:32](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 10:59:33](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 10:59:33](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 10:59:33](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 10:59:33](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 10:59:33](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 11:04:00](IMSClient): [SUCC] IMSClient master
[2025-06-28 11:04:00](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 11:04:00](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 11:04:01](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 11:04:01](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 11:04:01](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 11:04:01](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 11:04:01](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 11:04:01](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 11:04:01](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 11:04:01](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 11:04:02](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 11:04:02](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 11:04:02](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 11:04:02](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 11:04:02](SWuClient): [SUCC] SWu connection established
[2025-06-28 11:04:02](IMSClient): [INFO] P-CSCF Addr: fd00:976a:c307:30::5
[2025-06-28 11:04:02](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:2ccf:8454:ac39:8fb3:ca77:a9b5]
[2025-06-28 11:04:02](IMSSocketPool): [INFO] Self Port Initial: 7520, Client: 59944, Server: 35594
[2025-06-28 11:04:02](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 11:04:03](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:c307:30::5]:5060
[2025-06-28 11:04:03](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 11:04:03](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 11:04:03](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:2ccf:8454:ac39:8fb3:ca77:a9b5]:35594
[2025-06-28 11:04:03](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:c307:30::5]:65529
[2025-06-28 11:04:03](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 11:04:04](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 11:04:04](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 11:04:04](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 11:04:04](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 11:04:04](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 11:09:16](IMSClient): [SUCC] IMSClient master
[2025-06-28 11:09:16](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 11:09:16](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 11:09:17](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 11:09:17](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 11:09:17](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 11:09:17](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 11:09:17](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 11:09:17](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 11:09:17](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 11:09:18](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 11:09:18](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 11:09:18](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 11:09:18](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 11:09:18](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 11:09:18](SWuClient): [SUCC] SWu connection established
[2025-06-28 11:09:18](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:251::6
[2025-06-28 11:09:18](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:c1a5:3704:ac39:900b:4d73:79bd]
[2025-06-28 11:09:18](IMSSocketPool): [INFO] Self Port Initial: 17074, Client: 53314, Server: 35902
[2025-06-28 11:09:18](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 11:09:19](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:251::6]:5060
[2025-06-28 11:09:19](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 11:09:19](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 11:09:19](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:c1a5:3704:ac39:900b:4d73:79bd]:35902
[2025-06-28 11:09:20](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:251::6]:60077
[2025-06-28 11:09:20](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 11:09:20](IMSClient): [FAIL] REGISTER failed, check credentials or try again later
#! /home/<USER>/workspace/vowifi/IMSClient/imsclient/protocol/ims/RegisterSequence.php:118
#0 /home/<USER>/workspace/vowifi/IMSClient/imsclient/IMSClient.php(240): imsclient\protocol\ims\RegisterSequence->state_REGISTER_CONFIRM()
#1 /home/<USER>/workspace/vowifi/IMSClient/imsclient/IMSClient.php(153): imsclient\IMSClient->processStatusPacket()
#2 /home/<USER>/workspace/vowifi/IMSClient/imsclient/network/IMSSocket.php(77): imsclient\IMSClient->parsePacket()
#3 [internal function]: imsclient\network\IMSSocket->onData()
#4 /home/<USER>/workspace/vowifi/IMSClient/imsclient/network/EventSocket.php(355): call_user_func()
#5 /home/<USER>/workspace/vowifi/IMSClient/imsclient/IMSClient.php(98): imsclient\network\EventSocket::select()
#6 /home/<USER>/workspace/vowifi/IMSClient/main.php(40): imsclient\IMSClient->run()
#7 {main}
[2025-06-28 11:09:20](IMSClient): [FAIL] System can't continue, exiting...
[2025-06-28 11:09:20](IMSClient): [SUCC] Bye
[2025-06-28 11:09:34](IMSClient): [SUCC] IMSClient master
[2025-06-28 11:09:34](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 11:09:34](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 11:09:35](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 11:09:35](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 11:09:35](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 11:09:35](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 11:09:35](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 11:09:35](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 11:09:35](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 11:09:35](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 11:09:36](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 11:09:36](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 11:09:36](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 11:09:36](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 11:09:36](SWuClient): [SUCC] SWu connection established
[2025-06-28 11:09:36](IMSClient): [INFO] P-CSCF Addr: fd00:976a:d6b7:4::5
[2025-06-28 11:09:36](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:8a9d:a23:ac39:899b:9654:afae]
[2025-06-28 11:09:36](IMSSocketPool): [INFO] Self Port Initial: 32634, Client: 53355, Server: 64322
[2025-06-28 11:09:36](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 11:09:36](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:d6b7:4::5]:5060
[2025-06-28 11:09:36](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 11:09:37](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 11:09:37](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:8a9d:a23:ac39:899b:9654:afae]:64322
[2025-06-28 11:09:37](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:d6b7:4::5]:65529
[2025-06-28 11:09:37](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 11:09:37](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 11:09:37](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 11:09:37](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 11:09:38](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 11:09:38](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 11:21:05](IMSClient): [SUCC] IMSClient master
[2025-06-28 11:21:05](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 11:21:05](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 11:21:05](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 11:21:05](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 11:21:05](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 11:21:05](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 11:21:05](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 11:21:05](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 11:21:06](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 11:21:06](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 11:21:06](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 11:21:06](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 11:21:06](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 11:21:07](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 11:21:07](SWuClient): [SUCC] SWu connection established
[2025-06-28 11:21:07](IMSClient): [INFO] P-CSCF Addr: fd00:976a:c202:1808::5
[2025-06-28 11:21:07](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4]
[2025-06-28 11:21:07](IMSSocketPool): [INFO] Self Port Initial: 19998, Client: 33780, Server: 26779
[2025-06-28 11:21:07](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 11:21:07](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:c202:1808::5]:5060
[2025-06-28 11:21:07](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 11:21:08](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 11:21:08](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:bf1e:21d8:ac39:d5fa:7d6e:2e4]:26779
[2025-06-28 11:21:08](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:c202:1808::5]:65529
[2025-06-28 11:21:08](RegisterSequence): [INFO] Initial REGISTER...
