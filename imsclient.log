[2025-06-28 8:02:04](IMSClient): [SUCC] IMSClient master
[2025-06-28 8:02:04](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 8:02:04](PCSC<PERSON><PERSON><PERSON><PERSON>ider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 8:02:04](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 8:02:04](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 8:02:04](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 8:02:04](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 8:02:04](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 8:02:04](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 8:02:05](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 8:02:05](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 8:02:05](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 8:02:05](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 8:02:05](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 8:02:06](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 8:02:06](SWuClient): [SUCC] SWu connection established
[2025-06-28 8:02:06](IMSClient): [INFO] P-CSCF Addr: fd00:976a:14ef:50::5
[2025-06-28 8:02:06](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:ddb1:a697:ac39:909b:6576:7a4a]
[2025-06-28 8:02:06](IMSSocketPool): [INFO] Self Port Initial: 22076, Client: 22737, Server: 43768
[2025-06-28 8:02:06](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 8:02:06](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:14ef:50::5]:5060
[2025-06-28 8:02:06](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 8:02:06](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 8:02:06](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:ddb1:a697:ac39:909b:6576:7a4a]:43768
[2025-06-28 8:02:07](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:14ef:50::5]:65529
[2025-06-28 8:02:07](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 8:02:07](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 8:02:07](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 8:02:07](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 8:02:08](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 8:02:08](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 8:02:09](IMSClient): [SUCC] Bye
[2025-06-28 9:46:05](IMSClient): [SUCC] IMSClient master
[2025-06-28 9:46:05](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 9:46:05](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 9:46:05](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 9:46:05](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 9:46:05](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 9:46:05](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 9:46:05](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 9:46:05](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 9:46:06](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 9:46:06](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 9:46:06](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 9:46:06](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 9:46:06](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 9:46:07](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 9:46:07](SWuClient): [SUCC] SWu connection established
[2025-06-28 9:46:07](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:13d::5
[2025-06-28 9:46:07](IMSClient): [SUCC] Bye
[2025-06-28 9:49:44](IMSClient): [SUCC] IMSClient master
[2025-06-28 9:49:44](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 9:49:44](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 9:49:45](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 9:49:45](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 9:49:45](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 9:49:45](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 9:49:45](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 9:49:45](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 9:49:45](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 9:49:45](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 9:49:46](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 9:49:46](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 9:49:46](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 9:49:47](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 9:49:47](SWuClient): [SUCC] SWu connection established
[2025-06-28 9:49:47](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:131::5
[2025-06-28 9:49:47](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:d398:39bd:ac39:8feb:4d5b:b14c]
[2025-06-28 9:49:47](IMSSocketPool): [INFO] Self Port Initial: 52672, Client: 37787, Server: 52294
[2025-06-28 9:49:47](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 9:49:47](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:131::5]:5060
[2025-06-28 9:49:47](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 9:49:47](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 9:49:47](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:d398:39bd:ac39:8feb:4d5b:b14c]:52294
[2025-06-28 9:49:48](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:131::5]:65529
[2025-06-28 9:49:48](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 9:49:48](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 9:49:48](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 9:49:48](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 9:49:48](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 9:49:48](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 9:50:04](IMSClient): [SUCC] Bye
[2025-06-28 9:59:29](IMSClient): [SUCC] IMSClient master
[2025-06-28 9:59:29](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 9:59:29](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 9:59:30](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 9:59:30](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 9:59:30](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 9:59:30](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 9:59:30](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 9:59:30](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 9:59:30](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 9:59:30](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 9:59:31](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 9:59:31](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 9:59:31](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 9:59:31](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 9:59:31](SWuClient): [SUCC] SWu connection established
[2025-06-28 9:59:31](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:15b::5
[2025-06-28 9:59:31](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:d390:6c30:ac39:8feb:4b7b:be1b]
[2025-06-28 9:59:31](IMSSocketPool): [INFO] Self Port Initial: 27405, Client: 47711, Server: 53113
[2025-06-28 9:59:31](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 9:59:32](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:15b::5]:5060
[2025-06-28 9:59:32](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 9:59:32](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 9:59:32](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:d390:6c30:ac39:8feb:4b7b:be1b]:53113
[2025-06-28 9:59:32](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:15b::5]:65529
[2025-06-28 9:59:32](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 9:59:33](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 9:59:33](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 9:59:33](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 9:59:33](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 9:59:33](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 9:59:51](IMSClient): [SUCC] Bye
[2025-06-28 10:05:35](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:05:35](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:05:35](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:05:35](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:05:35](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:05:35](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:05:35](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:05:36](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:05:36](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 10:05:36](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 10:05:36](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 10:05:36](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 10:05:36](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 10:05:36](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 10:05:37](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 10:05:37](SWuClient): [SUCC] SWu connection established
[2025-06-28 10:05:37](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:143::5
[2025-06-28 10:05:37](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:cff2:a4b:ac39:8fd3:4bd4:12e0]
[2025-06-28 10:05:37](IMSSocketPool): [INFO] Self Port Initial: 16649, Client: 14019, Server: 29529
[2025-06-28 10:05:37](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 10:05:37](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:143::5]:5060
[2025-06-28 10:05:37](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 10:05:38](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 10:05:38](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:cff2:a4b:ac39:8fd3:4bd4:12e0]:29529
[2025-06-28 10:05:38](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:143::5]:65529
[2025-06-28 10:05:38](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 10:05:38](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 10:05:38](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 10:05:38](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 10:05:39](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 10:05:39](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 10:05:56](IMSClient): [SUCC] Bye
[2025-06-28 10:22:48](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:22:48](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:22:48](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:22:49](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:22:49](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:22:49](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:22:49](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:22:49](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:22:49](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 10:22:49](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 10:22:49](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 10:22:50](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 10:22:50](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 10:22:50](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 10:22:50](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 10:22:50](SWuClient): [SUCC] SWu connection established
[2025-06-28 10:22:50](IMSClient): [INFO] P-CSCF Addr: fd00:976a:c207:25::5
[2025-06-28 10:22:50](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:5e5d:1e2e:ac39:52ba:6463:9a89]
[2025-06-28 10:22:50](IMSSocketPool): [INFO] Self Port Initial: 53207, Client: 11303, Server: 58187
[2025-06-28 10:22:50](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 10:22:51](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:c207:25::5]:5060
[2025-06-28 10:22:51](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 10:22:52](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 10:22:52](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:5e5d:1e2e:ac39:52ba:6463:9a89]:58187
[2025-06-28 10:22:52](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:c207:25::5]:65529
[2025-06-28 10:22:52](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 10:22:52](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 10:22:52](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 10:22:52](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 10:22:53](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 10:22:53](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 10:23:09](IMSClient): [SUCC] Bye
[2025-06-28 10:28:46](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:28:46](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:28:46](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:28:46](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:28:46](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:28:46](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:28:46](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:28:46](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:28:46](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 10:28:47](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 10:28:47](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 10:28:47](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 10:28:47](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 10:28:47](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 10:28:48](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 10:28:48](SWuClient): [SUCC] SWu connection established
[2025-06-28 10:28:48](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:115::5
[2025-06-28 10:28:48](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:147:8e1f:ac39:883a:6543:9041]
[2025-06-28 10:28:48](IMSSocketPool): [INFO] Self Port Initial: 35645, Client: 40239, Server: 63632
[2025-06-28 10:28:48](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 10:28:48](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:115::5]:5060
[2025-06-28 10:28:48](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 10:28:48](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 10:28:49](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:147:8e1f:ac39:883a:6543:9041]:63632
[2025-06-28 10:28:49](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:115::5]:65529
[2025-06-28 10:28:49](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 10:28:49](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 10:28:49](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 10:28:49](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 10:28:50](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 10:28:50](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 10:29:14](IMSClient): [SUCC] Bye
[2025-06-28 10:37:32](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:37:32](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:37:32](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:37:32](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:37:33](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:37:33](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:37:33](IMSClient): [SUCC] Bye
[2025-06-28 10:44:16](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:44:16](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:44:16](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:44:16](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:44:16](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:44:16](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:44:16](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:44:16](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:44:16](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 10:44:17](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 10:44:17](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 10:44:17](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 10:44:17](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 10:44:17](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 10:44:18](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 10:44:18](SWuClient): [SUCC] SWu connection established
[2025-06-28 10:44:18](IMSClient): [INFO] P-CSCF Addr: fd00:976a:c202:1808::5
[2025-06-28 10:44:18](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:9b9e:2e0c:ac39:8a33:7d7f:87e0]
[2025-06-28 10:44:18](IMSSocketPool): [INFO] Self Port Initial: 62608, Client: 57932, Server: 63499
[2025-06-28 10:44:18](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 10:44:18](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:c202:1808::5]:5060
[2025-06-28 10:44:18](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 10:44:19](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 10:44:19](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:9b9e:2e0c:ac39:8a33:7d7f:87e0]:63499
[2025-06-28 10:44:19](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:c202:1808::5]:65529
[2025-06-28 10:44:19](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 10:44:19](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 10:44:19](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 10:44:19](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 10:44:20](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 10:44:20](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 10:47:08](IMSClient): [SUCC] Bye
[2025-06-28 10:48:18](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:48:18](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:48:18](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:48:19](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:48:19](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:48:19](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:48:19](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:48:19](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:48:19](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 10:48:19](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 10:48:19](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 10:48:20](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 10:48:20](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 10:48:20](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 10:48:20](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-28 10:48:20](SWuClient): [SUCC] SWu connection established
[2025-06-28 10:48:20](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:23d::5
[2025-06-28 10:48:20](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:f249:91a5:ac39:90da:64c7:38ce]
[2025-06-28 10:48:20](IMSSocketPool): [INFO] Self Port Initial: 61850, Client: 11762, Server: 15822
[2025-06-28 10:48:20](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 10:48:21](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:23d::5]:5060
[2025-06-28 10:48:21](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 10:48:21](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 10:48:21](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:f249:91a5:ac39:90da:64c7:38ce]:15822
[2025-06-28 10:48:22](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:23d::5]:65529
[2025-06-28 10:48:22](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 10:48:22](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 10:48:22](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 10:48:22](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 10:48:23](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 10:48:23](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 10:48:24](IMSClient): [SUCC] Bye
[2025-06-28 10:49:12](IMSClient): [SUCC] IMSClient master
[2025-06-28 10:49:12](PCSCUICCProvider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 10:49:12](PCSCUICCProvider): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 10:49:12](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 10:49:12](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 10:49:12](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 10:49:12](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 10:49:13](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 10:49:13](IMSClient): [SUCC] Bye
