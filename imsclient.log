[2025-06-28 6:46:32](IMSClient): [SUC<PERSON>] IMSClient master
[2025-06-28 6:46:32](PCSCUICC<PERSON><PERSON>ider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-28 6:46:32](PCSC<PERSON><PERSON><PERSON><PERSON><PERSON>): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-28 6:46:32](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-28 6:46:32](Utils): [DEBUG] ip link del ims8ffdc031
Cannot find device "ims8ffdc031"
[2025-06-28 6:46:32](Utils): [DEBUG] ip xfrm state deleteall reqid 2415771697
[2025-06-28 6:46:32](SWuClient): [INFO] SWu connection initializing...
[2025-06-28 6:46:32](Utils): [DEBUG] iptables -t nat -I OUTPUT -m mark --mark 2415771697 -d ************** -j DNAT --to-destination ***********
[2025-06-28 6:46:32](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-28 6:46:32](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-28 6:46:32](SWuClient): [DEBUG] Sending NAT packet size=508 hex=00000000b6760a0b00000000000000002120220800000000000001fc2200010c0200002c010100040300000c0100000c800e00800300000803000001030000080200000100000008040000020200002c020100040300000c0100000c800e01000300000803000001030000080200000100000008040000020200002c030100040300000c0100000c800e00800300000803000002030000080200000200000008040000020200002c040100040300000c0100000c800e01000300000803000002030000080200000200000008040000020200002c050100040300000c0100000c800e0080030000080300000c030000080200000500000008040000020000002c060100040300000c0100000c800e0100030000080300000c03000008020000050000000804000002280000880002000089cf7f6aaeac24748050d134c82ad573bd768c65a38e12c27df9f1ebee610cd69c3c6b147669e06d68b2c15cf51f0561df2ac612d94eb035b64e5ed9e28364cbc4b2f8b6e53dc23a4155f88279784db51b870ed503f90bd60ce72f04ff319ee97dcc64f01507eb60b50fbf84eff094c7646791cb2860be4bb20d11235eea0467290000146ad85f879a8236e5540a63d75809f2052900001c0000400400000000000000000000000000000000000000000000001c00004005cc8d6bba9004ecb61654209f3200b3acff5c1539
[2025-06-28 6:46:32](SWuClient): [DEBUG] Received NAT packet size=304 hex=00000000b6760a0b393cc85fb6013e05212022200000000000000130220000300000002c010100040300000c0100000c800e00800300000803000001030000080200000100000008040000022800008800020000313e1d30198d610ac7d52d706820a93b8b0efd14f91e2e3e47a40244fffd5220e25d2949160bae9533a092802755cc6cca711b601146a0b32d4ab2115b56e534ae0b68e70de60e2c897c5b4ed19337fa7b2d38ab2765b3e71c4e641dd6a9ca9c40892d97ae4481c3037093ffac597ab3c9c95e6fdedc85cb202f5e0cf5da9cc529000024647b3f7f764412627c85753bf1abf3b30f729863e36d4b0c3717962692b7b7442900001c000040040afd74f8925c0c165ebc3faf2a222f82a59006f60000001c0000400519ecac7a30dfcc2b40350f6484bbe2436a16cc2e
[2025-06-28 6:46:32](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-28 6:46:32](SWuClient): [DEBUG] IKE_SA_INIT - Selected algorithms: ENCR=12, ENCR_LEN=128, INTEG=1, PRF=1, DH_GROUP=2
[2025-06-28 6:46:32](SWuClient): [DEBUG] Decrypt Table: 00000000b6760a0b,393cc85fb6013e05,1dfbf293e0f159ccc37a62e328b9de52,ba051cddcb6c0d253e24e15c2b25b543,"AES-CBC-128 [RFC3602]",b513f97ea73f292c3978068c17d324e3,668f5a8b5743ed2393eff2acc3774cf9,"HMAC_MD5_96 [RFC2403]"
[2025-06-28 6:46:32](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-28 6:46:32](SWuClient): [DEBUG] Sending NAT-T packet size=572 hex=00000000b6760a0b393cc85fb6013e052e202308000000010000023c23000220a72474dcdb5ad92fe2ddd3cfabea7b9b4a6ecc7bd6add2ea56e9b4dd53b41817ea9c29320d5b06f1b55da32c2f6ab58bf0789eea6c5b977279422ca03781851606ebd415134d5e7abb4e001fb948e33491c619bf32d2948ee2474bca6567df1de973726fda865fbc7527f6fa82e64d46195bb0d7b8e500063d52ce4d3a8c9af1a245c163bad4a9c52164c227789ea4b14d22ecb13da8359f5ed6c718a2b09f1bc8f25b522b39a686ff11f78c064df4eb14b7508cd72531bc19899a423445097416b46c9b9548a02fd4da588e1694ef2d64ac13f3aaa78e19fa6993da233d89116c47a7417312ef13416af76ce0d8b3c3323c9a469eaf60a5f01ae2986a36248a6bc84bfbb5357279117e15675950df63c6ae6faacb0333c15f8cfe44e38bc5f998eaab39ffe7e52f1cea54e8c0de4759cdc5c2c251181fdbba7bcf261ade5ce04cbb4ad7abe0c784721857788036a9913e279ba8561c9e2aa4d913cf32e61f416d769c89d63768736ce57d434a24ded0aefc3722f1c7aa9a78c3ed52b803de72c7cef44b5b3434a74cbb8dd6a8f8a891a23fc66266704aae5440743634cad14c854bcc440a5bb45d00dcfa55048120e39d86e2e68e8c1b1b80bd77aac87cc3e07292e372da3be966a91432bac80796dbd05956d0f1014bf7847cc021d3a92cee05cf06c8472cc8cd0de40b9e1562bfe750b9bdf6deb2b7677dff88954492828fe83e2e326aed844770c28975e1a6a4f846166439376b2f87ca818c8d
[2025-06-28 6:46:33](SWuClient): [DEBUG] Received NAT-T packet size=188 hex=00000000b6760a0b393cc85fb6013e052e20232000000001000000bc240000a0a5fd04d70c777730dc1e5c1c32884779a73bb7c8da10dadb12662fc62cfa592309c06637fe306b49bac1e15a826d7230fcc6ee7adf29d3a068834b21e36e4e9594b112a01ff597bdf3cde23695ec1ce0231631cbd725fca8b6f4e711fbc8eeccb420f31c6185334aeb23a3ffb00ec62c7647517f754140390429f62c8eeaedb1d802a366c05ab82234afa4839249196bb36d8a5fe524c2ffe1efda8d
[2025-06-28 6:46:33](SWuClient): [INFO] Receive secure IKE MID=01
[2025-06-28 6:46:33](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-28 6:46:33](SWuClient): [DEBUG] Sending NAT-T packet size=108 hex=00000000b6760a0b393cc85fb6013e052e202308000000020000006c3000005004800c92683076cbb78d559ce411ff0fb584472b0fafb119af968000d28ec0f3c95d4daae60a621d8a4cecc9eec8545c85b0c3555ec2eddd86711d90d4dce1efdf6cbc349cd4926976d90845
[2025-06-28 6:46:33](SWuClient): [DEBUG] Received NAT-T packet size=76 hex=00000000b6760a0b393cc85fb6013e052e202320000000020000004c300000304b34b3a99676b92a1a4b69e7c386bf252ef02e5bd910a599747508f3dd48236065a855564ad3d551b2d76b20
[2025-06-28 6:46:33](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-28 6:46:33](SWuClient): [SUCC] EAP-AKA Success
[2025-06-28 6:46:33](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-28 6:46:33](SWuClient): [DEBUG] Sending NAT-T packet size=92 hex=00000000b6760a0b393cc85fb6013e052e202308000000030000005c2700004023d2e2483c6c3d6fd4fb1739d96ed29fb718f70f2a7f07f9b223c8939550d16e6ab901196d2945a08a551bb1d767d0fc7a09c39c662ab892ab75a6e0
[2025-06-28 6:46:34](SWuClient): [DEBUG] Received NAT-T packet size=364 hex=00000000b6760a0b393cc85fb6013e052e202320000000030000016c2700015036610abd4925678edb9c1dbaee9e54a7463792c730d5f3b66eb4b7a8f1b7c2e8d252f9d1da09bbfce232dd0121013633221d1bc07e610e5e23e09dd17d93a4e13d0843bb311bc20ed0e35284a15cb61269bb84d2d845a104eb67ea50c5486419127a5b532f923280ac216b51bc971b0d27a70606c5768c5f93ded32a71ee562f784318805c859725578c79b10727d2c9cbca3c1741552ffc6a0b2aaa5b60bbc5ec11c12a952e011fa200092dd1bff936a4a173dc49310213e17a915f155d64f6cd2060d1bf384e8ecbc657c33a697c26be93d9f5ee4f7c93b5af8c655f5be97ca0216ac9411f845031bc411bd09758ac753a8333c77676a131e1c72871e8397ac795c9f4acb12be21483d65fb7ca3c9472d02b3d4a978493c28c7f36be2c28d1df2ad56fc14cf0844aecbe0f900dc8b0f0b226e691fb295535f20e4ff065a4fa0bf4b9013fa87af434f2f0b7
[2025-06-28 6:46:34](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
PHP Warning:  Undefined property: imsclient\protocol\isakmp\packet\EncryptedPacket::$exchange_type in /home/<USER>/workspace/vowifi/IMSClient/imsclient/SWuClient.php on line 246
PHP Warning:  Undefined property: imsclient\protocol\isakmp\packet\EncryptedPacket::$flags in /home/<USER>/workspace/vowifi/IMSClient/imsclient/SWuClient.php on line 246
[2025-06-28 6:46:34](SWuClient): [DEBUG] IKE_AUTH Result - Exchange Type: , Flags:
[2025-06-28 6:46:34](SWuClient): [DEBUG] IKE_AUTH Result - Message ID: 3
[2025-06-28 6:46:34](Utils): [DEBUG] ip xfrm state add src ************ dst ************** proto esp spi 19323816 mode tunnel enc "cbc(aes)" "0xcfc5b3a88bd2ccc45e33618bb32a1967" auth-trunc "hmac(md5)" "0xa631089474d2251d98dedb7cf73ea3c3" 96 encap espinudp 59935 4500 0.0.0.0 sel src ::/0 dst ::/0 output-mark 2415771697 reqid 2415771697
[2025-06-28 6:46:34](Utils): [DEBUG] ip xfrm state add src ************** dst ************ proto esp spi 342320045 mode tunnel enc "cbc(aes)" "0x035664febcde9bbedfce7d91604c5cf3" auth-trunc "hmac(md5)" "0x5f8635fd43b2c1cb2571d30047324d1b" 96 encap espinudp 4500 59935 0.0.0.0 sel src ::/0 dst ::/0 reqid 2415771697
[2025-06-28 6:46:34](Utils): [DEBUG] ip xfrm policy add src ::/0 dst ::/0 dir out tmpl src ************ dst ************** proto esp spi 19323816 mode tunnel reqid 2415771697 mark 2415771697
[2025-06-28 6:46:34](Utils): [DEBUG] ip xfrm policy add src ::/0 dst ::/0 dir in tmpl src ************** dst ************ proto esp spi 342320045 mode tunnel reqid 2415771697 mark 2415771697
[2025-06-28 6:46:34](Utils): [DEBUG] ip link add ims8ffdc031 type vti local ************ remote ************** key 2415771697
[2025-06-28 6:46:34](Utils): [DEBUG] ip addr add 2607:fc20:9ba2:881d:ac39:8a33:64f2:9605/128 dev ims8ffdc031
[2025-06-28 6:46:34](Utils): [DEBUG] ip link set ims8ffdc031 mtu 1280 up
[2025-06-28 6:46:34](Utils): [DEBUG] ip route add fd00:976a:2:14b::5 src 2607:fc20:9ba2:881d:ac39:8a33:64f2:9605 dev ims8ffdc031
[2025-06-28 6:46:34](SWuClient): [SUCC] SWu connection established
[2025-06-28 6:46:34](IMSClient): [INFO] P-CSCF Addr: fd00:976a:2:14b::5
[2025-06-28 6:46:34](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:9ba2:881d:ac39:8a33:64f2:9605]
[2025-06-28 6:46:34](IMSSocketPool): [DEBUG] Creating sockets...
[2025-06-28 6:46:34](IMSSocketPool): [INFO] Self Port Initial: 43254, Client: 61351, Server: 47159
[2025-06-28 6:46:34](IMSSocketPool): [INFO] Starting sockets...
[2025-06-28 6:46:34](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:2:14b::5]:5060
[2025-06-28 6:46:34](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-28 6:46:35](RegisterSequence): [INFO] Received server challenge & IPSec configration
[2025-06-28 6:46:35](Identity): [DEBUG] Digest Challenge H(Response): 0d379cdd515e3c03e1ff33cf759bd703
[2025-06-28 6:46:35](Utils): [DEBUG] ip xfrm state add src 2607:fc20:9ba2:881d:ac39:8a33:64f2:9605 dst fd00:976a:2:14b::5 proto esp spi 1271752451 mode transport enc "cipher_null" "" auth-trunc "hmac(md5)" "0x857877bc4c082cf2888049ad84374813" 96 reqid 2415771697
[2025-06-28 6:46:35](Utils): [DEBUG] ip xfrm state add src fd00:976a:2:14b::5 dst 2607:fc20:9ba2:881d:ac39:8a33:64f2:9605 proto esp spi 1177389415 mode transport enc "cipher_null" "" auth-trunc "hmac(md5)" "0x857877bc4c082cf2888049ad84374813" 96 reqid 2415771697
[2025-06-28 6:46:35](Utils): [DEBUG] ip xfrm policy add src 2607:fc20:9ba2:881d:ac39:8a33:64f2:9605 dst fd00:976a:2:14b::5 dir out tmpl src 2607:fc20:9ba2:881d:ac39:8a33:64f2:9605 dst fd00:976a:2:14b::5 proto esp spi 1271752451 mode transport reqid 2415771697 mark 2684207153
[2025-06-28 6:46:35](Utils): [DEBUG] ip xfrm state add src 2607:fc20:9ba2:881d:ac39:8a33:64f2:9605 dst fd00:976a:2:14b::5 proto esp spi 1271752450 mode transport enc "cipher_null" "" auth-trunc "hmac(md5)" "0x857877bc4c082cf2888049ad84374813" 96 reqid 2415771697
[2025-06-28 6:46:35](Utils): [DEBUG] ip xfrm state add src fd00:976a:2:14b::5 dst 2607:fc20:9ba2:881d:ac39:8a33:64f2:9605 proto esp spi 3832599175 mode transport enc "cipher_null" "" auth-trunc "hmac(md5)" "0x857877bc4c082cf2888049ad84374813" 96 reqid 2415771697
[2025-06-28 6:46:35](Utils): [DEBUG] ip xfrm policy add src 2607:fc20:9ba2:881d:ac39:8a33:64f2:9605 dst fd00:976a:2:14b::5 dir out tmpl src 2607:fc20:9ba2:881d:ac39:8a33:64f2:9605 dst fd00:976a:2:14b::5 proto esp spi 1271752450 mode transport reqid 2415771697 mark 2952642609
[2025-06-28 6:46:35](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:9ba2:881d:ac39:8a33:64f2:9605]:47159
[2025-06-28 6:46:35](IMSSocketPool): [DEBUG] Connecting to secured endpoint: [fd00:976a:2:14b::5]:65529...
[2025-06-28 6:46:35](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:2:14b::5]:65529
[2025-06-28 6:46:35](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-28 6:46:35](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-28 6:46:35](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-28 6:46:35](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-28 6:46:36](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-28 6:46:36](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-28 6:46:36](IMSClient): [DEBUG] Received NOTIFY, simply ACK 200 OK and ignore...
^CPHP Warning:  socket_select(): Unable to select [4]: Interrupted system call in /home/<USER>/workspace/vowifi/IMSClient/imsclient/network/EventSocket.php on line 336
[2025-06-28 6:46:48](Utils): [DEBUG] ip link del ims8ffdc031
[2025-06-28 6:46:48](Utils): [DEBUG] ip xfrm policy delete src 2607:fc20:9ba2:881d:ac39:8a33:64f2:9605/128 dst fd00:976a:2:14b::5/128 dir out mark 0xaffdc031
[2025-06-28 6:46:48](Utils): [DEBUG] ip xfrm policy delete src 2607:fc20:9ba2:881d:ac39:8a33:64f2:9605/128 dst fd00:976a:2:14b::5/128 dir out mark 0x9ffdc031
[2025-06-28 6:46:48](Utils): [DEBUG] ip xfrm policy delete src ::/0 dst ::/0 dir in mark 0x8ffdc031
[2025-06-28 6:46:48](Utils): [DEBUG] ip xfrm policy delete src ::/0 dst ::/0 dir out mark 0x8ffdc031
[2025-06-28 6:46:48](Utils): [DEBUG] ip xfrm state deleteall reqid 2415771697
[2025-06-28 6:46:48](Utils): [DEBUG] iptables -t nat -D OUTPUT -d ************** -m mark --mark 0x8ffdc031 -j DNAT --to-destination ***********
[2025-06-28 6:46:48](IMSClient): [SUCC] Bye
